const app = getApp();
import {
  querySiteDetailApi,
  querySitDevLivingListApi,
  liveReplay,
  getDeviceStatus,
} from "@/api/site";
import {
  addWatchLiveRecordApi,
  slicingTaskPage,
  submitSlicingTask,
  deleteSlicingTask,
  acquiringRights,
} from "@/api/live";
import { sliceVideoCreateOrderApi } from "@/api/order";
import { payOrderApi } from "@/api/pay";

Page({
  data: {
    paddingHeight: app.globalData.paddingHeight,
    navData: {
      title: "",
      color: "#ffffff",
    },
    isRecording: false,
    isClipping: false,
    baseTime: Date.now(),
    currentTime: Date.now(),
    pixelsPerSecond: 10,
    containerWidth: 0,
    scaleUnit: "second",
    scaleUnitText: "秒",
    scaleLevel: 0,
    recordStartTime: 0,
    recordEndTime: 0,
    timeScales: [],
    formattedCurrentTime: "",

    sliderMin: 0,
    sliderMax: 100,
    sliderStep: 1,
    sliderValue: 50,
    timeRange: 24 * 60 * 60 * 1000,

    clippingStartTime: null,
    leftBoundaryTime: null,

    userRights: {
      total: 0,
      remain: 0,
      remainFormatted: "00:00:00",
    },

    showTutorial: false,
    tutorialStep: 1,
    tutorialHighlightStyle: null,
    tutorialHighlightReady: false,
    tutorialData: {
      1: {
        title: "点击剪辑按钮",
        content:
          "点击左下角的剪辑按钮，可以创建录制区域来选择要保存的视频片段。",
        target: "cut-icon",
        position: "top",
      },
      2: {
        title: "选择录制区域",
        content:
          "在手势区域内，您可以：拖拽两侧手柄调整录制区域、双指缩放调整刻度时长，快速的定位录制区域。",
        target: "gesture-operation-box",
        position: "top-center",
      },
      3: {
        title: "保存录制片段",
        content:
          '调整好录制区域后，点击右下角的"保存切片"按钮即可保存录像。系统会根据录制时长扣除相应的权益时长。',
        target: "cut-icon--save-mode",
        position: "bottom",
      },
    },
    recordAreaStyle: "",
    recordAreaAnimation: "",
    pausedTime: 0,
    recordStartTimeFormatted: "",
    recordEndTimeFormatted: "",

    recordDurationText: "",
    iconSize: "50rpx",

    isDragging: false,
    dragStartX: 0,
    dragStartTime: 0,

    isRecordAreaDragging: false,
    recordDragType: "",
    recordDragStartX: 0,
    recordDragOriginalStartTime: 0,
    recordDragOriginalEndTime: 0,

    isEdgeScrolling: false,
    edgeScrollTimer: null,
    edgeScrollSpeed: 0,
    edgeScrollHandleX: 0,

    handleDragStartY: 0,
    handleDragThreshold: 20,

    _isSwitchingCamera: false,

    _isNavigatingToVideo: false,
    _isLoadingNewVideoList: false,

    autoMoveBaselineOnHandleDrag: true,

    availableEquity: 0,

    shouldJumpToStartOnPlay: false,

    canZoomIn: true,
    canZoomOut: true,

    siteDetail: {},
    shareUrl: "",
    activateCameraIndex: 0,
    isMuted: false,
    isPlaying: false,
    swipeDeleteItemId: null,
    videoList: [],
    videoUrl: "",

    touchState: {
      startTime: null,
      startX: null,
      startY: null,
      moved: false,
    },
    _isLoadingVideoList: false,

    prePayInfo: {},

    timers: {
      status: null,
      recording: null,
    },
  },

  /**
   * 跳转到录制区域的开始时间
   * 验证录制区域和摄像头信息，调用回放接口跳转到录制开始时间点
   */
  jumpToRecordingStartTime: async function () {
    // 检查是否存在录制区域
    if (!this.data.isRecording || !this.data.recordStartTime) {
      wx.showToast({
        title: "当前没有录制区域",
        icon: "none",
        duration: 2000,
      });
      return;
    }

    const recordStartTime = this.data.recordStartTime;
    const cameraList = this.data.cameraList;
    const activateCameraIndex = this.data.activateCameraIndex;

    // 验证摄像头列表
    if (
      !cameraList ||
      cameraList.length === 0 ||
      activateCameraIndex < 0 ||
      activateCameraIndex >= cameraList.length
    ) {
      wx.showToast({
        title: "摄像头列表异常",
        icon: "none",
        duration: 2000,
      });
      return;
    }

    // 验证当前摄像头信息
    const currentCamera = cameraList[activateCameraIndex];
    if (!currentCamera || !currentCamera.id) {
      wx.showToast({
        title: "摄像头信息异常",
        icon: "none",
        duration: 2000,
      });
      return;
    }

    // 计算时间边界
    const leftBoundaryTime =
      this.data.leftBoundaryTime || Date.now() - 23 * 60 * 60 * 1000;
    const rightBoundaryTime = Date.now();

    // 检查录制开始时间是否在有效范围内
    if (recordStartTime < leftBoundaryTime) {
      wx.showToast({
        title: "录制开始时间超出可查看范围",
        icon: "none",
        duration: 2000,
      });
      return;
    }

    if (recordStartTime > rightBoundaryTime) {
      wx.showToast({
        title: "录制开始时间不能超过当前时间",
        icon: "none",
        duration: 2000,
      });
      return;
    }

    try {
      // 停止自动播放
      this.stopAutoPlay();

      wx.showLoading({
        title: "跳转到录制开始时间...",
        mask: true,
      });

      // 计算slider值并更新时间状态
      const sliderValue = this._timeToSliderValue(recordStartTime);

      this.setData({
        isPlaying: false,
        currentTime: recordStartTime,
        baseTime: recordStartTime,
        pausedTime: recordStartTime,
        sliderValue: sliderValue,
        formattedCurrentTime: this.formatTime(recordStartTime),
      });

      // 重新生成时间刻度和更新录制区域
      this.generateTimeScales();
      this._updateRecordAreaNew();

      // 调用回放接口获取视频链接
      const timestamp = Math.floor(recordStartTime / 1000);
      const res = await liveReplay({
        id: currentCamera.id,
        timeStamp: timestamp,
      });

      // 重置视频状态标志
      this._videoLoadedData = false;
      this._videoCanPlay = false;
      this._needPauseAfterLoad = true;

      // 更新视频链接
      this.setData({
        videoUrl: res.data,
        isPlaying: false,
      });
    } catch (error) {
      console.error("跳转到录制开始时间失败:", error);
      wx.showToast({
        title: "跳转失败，请重试",
        icon: "none",
        duration: 2000,
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 节流的视频预览方法
   * 在拖拽过程中延迟更新视频预览，避免频繁的API调用
   * @param {number} time - 预览的时间戳
   */
  _throttledVideoPreview: function (time) {
    if (this._videoPreviewTimer) {
      clearTimeout(this._videoPreviewTimer);
    }

    this._videoPreviewTimer = setTimeout(() => {
      if (!this.data.isDragging) {
        this._updateVideoUrlAfterDrag(time);
      }
    }, 500);
  },

  /**
   * 将时间戳对齐到指定的刻度间隔
   * @param {number} timestamp - 要对齐的时间戳
   * @param {number} interval - 刻度间隔（毫秒）
   * @returns {number} 对齐后的时间戳
   */
  _alignToScaleInterval: function (timestamp, interval) {
    return Math.round(timestamp / interval) * interval;
  },

  /**
   * 将时间戳对齐到当前刻度单位的边界
   * @param {number} timestamp - 要对齐的时间戳
   * @returns {number} 对齐后的时间戳
   */
  _alignToSecond: function (timestamp) {
    const scaleConfig = this._getScaleConfig(this.data.scaleUnit);
    const interval = scaleConfig.interval;

    return Math.round(timestamp / interval) * interval;
  },

  /**
   * 分析列表变化，比较当前列表和新列表的差异
   * @param {Array} currentList - 当前列表
   * @param {Array} newList - 新列表
   * @returns {Object} 包含新增、更新、删除和未变化项目的分析结果
   */
  _analyzeListChanges(currentList, newList) {
    const toAdd = [];
    const toUpdate = [];
    const toRemove = [];
    const unchanged = [];

    newList.forEach((newItem) => {
      const currentItem = currentList.find((item) => item.id === newItem.id);

      if (!currentItem) {
        toAdd.push({
          ...newItem,
          isNew: true,
        });
      } else {
        const hasChanges = this._detectItemChanges(currentItem, newItem);

        if (hasChanges.hasChange) {
          toUpdate.push({
            ...newItem,
            isUpdated: true,
            isUpdating: true,
            statusChangeType: hasChanges.changeType,
            previousStatus: currentItem.sliceStatue,
          });
        } else {
          unchanged.push({
            ...currentItem,
            isUpdated: false,
            isUpdating: false,
            statusChangeType: undefined,
          });
        }
      }
    });

    currentList.forEach((currentItem) => {
      const newItem = newList.find((item) => item.id === currentItem.id);
      if (!newItem) {
        toRemove.push(currentItem);
      }
    });

    return { toAdd, toUpdate, toRemove, unchanged };
  },

  /**
   * 构建最终的视频列表
   * @param {Object} updates - 更新信息对象
   * @param {Array} newList - 新的视频列表
   * @returns {Array} 构建完成的最终列表
   */
  _buildFinalList(updates, newList) {
    const { toAdd, toUpdate, toRemove, unchanged } = updates;

    // 创建删除项的Set，用于快速查找
    const removeSet = new Set(toRemove.map(item => item.id));

    // 只包含未被删除的项目
    const allItems = [...toAdd, ...toUpdate, ...unchanged].filter(
      item => !removeSet.has(item.id)
    );

    const sortedItems = this._sortVideoList(allItems);

    const finalList = sortedItems.map((item, index) => {
      const baseItem = this._transformVideoItem(item, index);

      if (toAdd.find((addItem) => addItem.id === item.id)) {
        return { ...baseItem, isNew: true };
      } else if (toUpdate.find((updateItem) => updateItem.id === item.id)) {
        const updateItem = toUpdate.find(
          (updateItem) => updateItem.id === item.id,
        );
        return {
          ...baseItem,
          isUpdated: true,
          isUpdating: true,
          statusChangeType: updateItem.statusChangeType,
        };
      } else {
        return { ...baseItem, isNew: false, isUpdated: false };
      }
    });

    return finalList;
  },

  /**
   * 计算对齐后的位置坐标
   * @param {number} time - 时间戳
   * @param {number} baseTime - 基准时间
   * @param {number} pixelsPerSecond - 每秒像素数
   * @param {number} containerWidth - 容器宽度
   * @param {number} trackOffset - 轨道偏移量
   * @returns {number} 对齐后的位置坐标
   */
  _calculateAlignedPosition: function (
    time,
    baseTime,
    pixelsPerSecond,
    containerWidth,
    trackOffset,
  ) {
    const relativeTime = (time - baseTime) / 1000;
    const relativePosition = relativeTime * pixelsPerSecond;
    const centerPosition = containerWidth / 2;
    const rawPosition = relativePosition + centerPosition + (trackOffset || 0);

    const pixelRatio = this.data.pixelRatio || 1;
    const deviceType = this.data.deviceType || "medium-phone";

    let alignedPosition;
    if (deviceType === "large-phone" || deviceType === "tablet") {
      alignedPosition = Math.round(rawPosition * pixelRatio) / pixelRatio;
    } else {
      alignedPosition = Math.round(rawPosition);
    }

    return alignedPosition;
  },

  /**
   * 启动简化的惯性动画
   * 适配slider系统，提供平滑的滑动体验
   * @param {number} initialVelocity - 初始速度
   */
  _startSimpleInertiaAnimation: function (initialVelocity) {
    if (this.inertiaAnimationId) {
      clearInterval(this.inertiaAnimationId);
    }

    let velocity = initialVelocity;
    const friction = 0.95;
    const minVelocity = 0.1;

    this.inertiaAnimationId = setInterval(() => {
      velocity *= friction;

      if (Math.abs(velocity) < minVelocity) {
        clearInterval(this.inertiaAnimationId);
        this.inertiaAnimationId = null;
        this._updateVideoUrlAfterDrag(this.data.currentTime);
        return;
      }

      const timeOffset = (velocity / this.data.pixelsPerSecond) * 1000 * 16;
      const newTime = this.data.currentTime - timeOffset;

      const { leftBoundaryTime } = this.data;
      const realCurrentTime = Date.now();

      if (
        (leftBoundaryTime && newTime < leftBoundaryTime) ||
        newTime > realCurrentTime
      ) {
        clearInterval(this.inertiaAnimationId);
        this.inertiaAnimationId = null;

        const boundaryTime =
          newTime < leftBoundaryTime ? leftBoundaryTime : realCurrentTime;
        const sliderValue = this._timeToSliderValue(boundaryTime);
        this.setData({
          currentTime: boundaryTime,
          sliderValue: sliderValue,
          formattedCurrentTime: this.formatTime(boundaryTime),
        });
        this.generateTimeScales();
        this._updateVideoUrlAfterDrag(boundaryTime);
        return;
      }

      this._updateTimeForDrag(newTime);
    }, 16);
  },

  /**
   * 拖拽时更新时间状态
   * 适配slider系统，提供边界检查和节流更新
   * @param {number} newTime - 新的时间戳
   */
  _updateTimeForDrag: function (newTime) {
    const { leftBoundaryTime } = this.data;
    const realCurrentTime = Date.now();

    let finalTime = newTime;

    if (leftBoundaryTime && finalTime < leftBoundaryTime) {
      finalTime = leftBoundaryTime;
    }

    if (finalTime > realCurrentTime) {
      finalTime = realCurrentTime;
    }

    const sliderValue = this._timeToSliderValue(finalTime);

    this.setData({
      currentTime: finalTime,
      sliderValue: sliderValue,
      formattedCurrentTime: this.formatTime(finalTime),
      pausedTime: finalTime,
    });

    if (!this._dragUpdateTimer) {
      this._dragUpdateTimer = setTimeout(() => {
        this.generateTimeScales();

        if (this.data.isRecording) {
          this._updateRecordAreaNew();
        }

        this._dragUpdateTimer = null;
      }, 16);
    }
  },

  /**
   * 初始化slider相关数据
   * 设置时间范围和slider的基本参数
   */
  _initSliderData: function () {
    const currentTime = Date.now();
    const leftBoundaryTime = currentTime - 23 * 60 * 60 * 1000;

    if (leftBoundaryTime >= currentTime) {
      console.error("_initSliderData: 时间范围无效");
      return;
    }

    this.setData({
      timeRangeStart: leftBoundaryTime,
      timeRangeEnd: currentTime,
      sliderMin: 0,
      sliderMax: 100,
      sliderValue: 100,
      sliderStep: 0.1,
    });
  },

  /**
   * 将时间戳转换为slider值
   * @param {number} time - 时间戳
   * @returns {number} slider值 (0-100)
   */
  _timeToSliderValue: function (time) {
    const { timeRangeStart, timeRangeEnd } = this.data;

    if (!timeRangeStart || !timeRangeEnd || timeRangeEnd <= timeRangeStart) {
      return 50;
    }

    const totalRange = timeRangeEnd - timeRangeStart;
    if (totalRange === 0) {
      return 50;
    }

    const timeOffset = time - timeRangeStart;
    let sliderValue = (timeOffset / totalRange) * 100;

    sliderValue = Math.max(0, Math.min(100, sliderValue));

    return Math.round(sliderValue);
  },

  /**
   * 将slider值转换为时间戳
   * @param {number} value - slider值 (0-100)
   * @returns {number} 对应的时间戳
   */
  _sliderValueToTime: function (value) {
    const { timeRangeStart, timeRangeEnd } = this.data;

    if (!timeRangeStart || !timeRangeEnd || timeRangeEnd <= timeRangeStart) {
      return Date.now();
    }

    const clampedValue = Math.max(0, Math.min(100, value || 0));

    const totalRange = timeRangeEnd - timeRangeStart;
    const resultTime = timeRangeStart + (clampedValue / 100) * totalRange;

    return Math.max(timeRangeStart, Math.min(timeRangeEnd, resultTime));
  },

  /**
   * 处理slider拖拽过程中的变化事件
   * @param {Object} e - 事件对象
   */
  onSliderChanging: function (e) {
    const sliderValue = e.detail.value;
    const newTime = this._sliderValueToTime(sliderValue);
    this.stopAutoPlay();

    // 更新时间和UI
    this.setData({
      sliderValue: sliderValue,
      currentTime: newTime,
      baseTime: newTime,
      isPlaying: false,
      formattedCurrentTime: this.formatTime(newTime),
    });

    // 重新生成刻度
    this.generateTimeScales();

    // 更新录制区域 - 使用新方法
    if (this.data.isRecording) {
      this._updateRecordAreaNew();
    }

    // 拖拽过程中的视频预览 - 节流处理
    this._throttledVideoPreview(newTime);
  },

  /**
   * slider 拖拽完成后的事件处理
   */
  onSliderChange: function (e) {
    const sliderValue = e.detail.value;
    const newTime = this._sliderValueToTime(sliderValue);

    // 更新最终状态 - 确保 pausedTime 被正确设置
    this.setData({
      sliderValue: sliderValue,
      currentTime: newTime,
      baseTime: newTime,
      pausedTime: newTime, // 关键：确保暂停时间被设置，避免播放时跳到实时时间
      isPlaying: false,
      formattedCurrentTime: this.formatTime(newTime),
    });

    // 重新生成刻度
    this.generateTimeScales();

    // 更新录制区域 - 使用新方法
    if (this.data.isRecording) {
      this._updateRecordAreaNew();
    }

    // 调用回放接口更新视频 - 添加防重复调用
    if (!this._isUpdatingVideo) {
      this._updateVideoUrlAfterDrag(newTime);
    }

    // 拖拽后保持暂停状态，不自动恢复播放
  },

  /**
   * 计算录像时长
   * @param {number} startTime 开始时间（秒）
   * @param {number} endTime 结束时间（秒）
   * @returns {string} 时长字符串
   */
  _calculateDuration(startTime, endTime) {
    if (!startTime || !endTime) return "00:00:00";

    const durationSeconds = Math.abs(endTime - startTime);
    const hours = Math.floor(durationSeconds / 3600);
    const minutes = Math.floor((durationSeconds % 3600) / 60);
    const seconds = Math.floor(durationSeconds % 60);

    return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
  },

  _calculateNearestScaleTime: function (time) {
    const scaleConfig = this._getScaleConfig(this.data.scaleUnit);
    const interval = scaleConfig.interval;

    // 将时间吸附到最近的刻度间隔
    const snappedTime = Math.round(time / interval) * interval;

    return snappedTime;
  },

  _calculateVelocity: function () {
    if (!this.velocityTracker || this.velocityTracker.positions.length < 2) {
      return 0;
    }

    const positions = this.velocityTracker.positions;
    const latest = positions[positions.length - 1];
    const earliest = positions[0];

    // 计算时间差（毫秒）
    const timeDiff = latest.time - earliest.time;
    if (timeDiff === 0) return 0;

    // 计算位置差（像素）
    const positionDiff = latest.x - earliest.x;

    // 返回速度（像素/毫秒）
    return positionDiff / timeDiff;
  },

  _checkAndShowTutorial: function () {
    // 检查用户是否已经完成过新手教程
    const hasCompletedTutorial =
      wx.getStorageSync("hasCompletedTutorial") || false;

    if (!hasCompletedTutorial) {
      // 暂停播放，让用户专注于教程
      this.stopAutoPlay();

      // 使用 wx.nextTick 确保 DOM 完全渲染后再显示教程
      wx.nextTick(() => {
        // 再次使用 setTimeout 确保所有动画和布局完成
        setTimeout(() => {
          this._showTutorialWithDynamicPosition();
        }, 300);
      });
    }
  },

  _checkAndUpdateScales: function (offset) {
    const {
      containerWidth,
      timeScales,
      isClipping,
      baseTime,
      pixelsPerSecond,
    } = this.data;

    // 只有在剪辑状态下才检查和更新刻度
    if (!isClipping || !containerWidth || !pixelsPerSecond) {
      return;
    }

    // 如果正在跳转，跳过刻度扩展检查以避免过度日志输出
    if (this._isJumping) {
      return;
    }

    // 防止重复渲染：如果正在生成刻度，跳过
    if (this._isGeneratingScales) {
      return;
    }

    // 添加异常处理，确保刻度检查不会因为异常而中断
    try {
      // 如果没有刻度或刻度数量过少，立即生成
      if (!timeScales || timeScales.length < 10) {
        this.generateTimeScales();
        return;
      }

      // 优化防抖机制：增加刻度切换时的节流处理
      const now = Date.now();
      let debounceTime = 60; // 默认防抖时间

      if (this.data.isDragging || this.data.isRecordAreaDragging) {
        debounceTime = 16; // 拖拽时减少防抖时间，提高响应性
      } else if (this.data.isPlaying) {
        debounceTime = 30; // 播放时使用较短的防抖时间
      }

      // 特殊处理：如果正在进行刻度切换，跳过检查避免重复渲染
      if (this._isScaleSwitching) {
        return; // 刻度切换时直接跳过检查，避免重复渲染
      }

      if (
        this._lastScaleCheckTime &&
        now - this._lastScaleCheckTime < debounceTime
      ) {
        return;
      }
      this._lastScaleCheckTime = now;

      // 计算当前可视范围的边界，使用更大的缓冲区确保无限预加载
      const bufferWidth = containerWidth * 2.5; // 增加到250%的缓冲区，确保足够的预加载
      const leftBoundary = -offset - bufferWidth;
      const rightBoundary = containerWidth - offset + bufferWidth;

      // 重新计算刻度位置，确保准确性
      const currentScalePositions = timeScales
        .map((scale) => {
          return this._calculateAlignedPosition(
            scale.time,
            baseTime,
            pixelsPerSecond,
            containerWidth,
            offset,
          );
        })
        .filter((pos) => pos !== undefined && !isNaN(pos));

      if (currentScalePositions.length === 0) {
        this.generateTimeScales();
        return;
      }

      const leftmostScale = Math.min(...currentScalePositions);
      const rightmostScale = Math.max(...currentScalePositions);

      // 更积极的刻度生成策略：当接近边界时就开始生成新刻度
      const earlyTriggerBuffer = containerWidth * 0.8; // 提前触发缓冲区
      let needLeftExtension = leftmostScale > leftBoundary + earlyTriggerBuffer;
      const needRightExtension =
        rightmostScale < rightBoundary - earlyTriggerBuffer;

      // 新增：检查左边界限制，如果已经到达边界则不再向左扩展
      const { leftBoundaryTime } = this.data;
      if (leftBoundaryTime && needLeftExtension) {
        // 安全地获取最小时间，避免只读错误
        let leftmostTime = Infinity;
        for (let i = 0; i < timeScales.length; i++) {
          if (timeScales[i] && typeof timeScales[i].time === "number") {
            leftmostTime = Math.min(leftmostTime, timeScales[i].time);
          }
        }
        const scaleConfig = this._getScaleConfig(this.data.scaleUnit);
        const interval = scaleConfig.interval;

        // 更严格的边界检查：如果最左侧刻度已经在边界附近（一个间隔内），则不再扩展
        if (leftmostTime <= leftBoundaryTime + interval) {
          needLeftExtension = false; // 已经到达或接近左边界，不再向左扩展
        }
      }

      if (needLeftExtension || needRightExtension) {
        this._extendTimeScales(needLeftExtension, needRightExtension, offset);
      }
    } catch (error) {
      console.error("刻度检查异常:", error);
      // 异常时强制重新生成刻度
      this.generateTimeScales();
    }
  },

  /**
   * 检查是否需要更新摄像头列表
   * @param {Array} newList 新的摄像头列表
   * @returns {boolean} 是否需要更新
   */
  _checkIfCameraNeedUpdate(newList) {
    const currentList = this.data.cameraList;

    if (!currentList || newList.length !== currentList.length) {
      return true;
    }

    // 创建当前摄像头ID的集合
    const currentCameraIds = new Set(currentList.map((camera) => camera.id));

    // 检查新列表中的每个摄像头是否存在于当前列表中
    for (const newItem of newList) {
      if (!currentCameraIds.has(newItem.id)) {
        return true;
      }
    }

    return false;
  },

  /**
   * 清除所有定时器
   */
  _clearAllTimers() {
    Object.keys(this.data.timers).forEach((name) => {
      this._clearTimer(name);
    });
  },

  /**
   * 清除指定定时器
   * @param {string} name 定时器名称
   */
  _clearTimer(name) {
    const timer = this.data.timers[name];
    if (timer) {
      clearInterval(timer);
      this.setData({
        [`timers.${name}`]: null,
      });
    }
  },

  _completeTutorial: function () {
    // 隐藏教程
    this.setData({
      showTutorial: false,
      tutorialStep: 1,
    });

    // 取消教程期间创建的录制区域
    if (this.data.isRecording) {
      this.setData({
        isRecording: false,
        recordStartTime: 0,
        recordEndTime: 0,
        recordStartTimeFormatted: "",
        recordEndTimeFormatted: "",
        recordDurationText: "",
        recordAreaStyle: "display: none;",
        recordAreaAnimation: "",
        // 重置录制相关的拖拽状态
        isRecordAreaDragging: false,
        recordDragType: "",
        recordDragStartX: 0,
        recordDragOriginalStartTime: 0,
        recordDragOriginalEndTime: 0,
      });
    }

    // 跳转到最新时间并自动播放
    const currentRealTime = Date.now();
    const sliderValue = this._timeToSliderValue(currentRealTime);

    this.setData({
      baseTime: currentRealTime,
      currentTime: currentRealTime,
      sliderValue: sliderValue,
      formattedCurrentTime: this.formatTime(currentRealTime),
      pausedTime: 0, // 重置暂停时间
      isPlaying: true, // 恢复播放状态
      // 更新slider时间范围到最新
      timeRangeEnd: currentRealTime,
    });

    // 延迟重新绘制，确保数据更新完成
    setTimeout(() => {
      this._redrawAfterTutorial();
    }, 100);

    // 保存完成状态到本地存储
    wx.setStorageSync("hasCompletedTutorial", true);

    // 显示完成提示
    wx.showToast({
      title: "教程完成！开始使用吧",
      icon: "success",
      duration: 2000,
    });
  },

  _createNewRecordArea: function () {
    // 保存操作前的状态
    const beforeState = {
      isRecording: this.data.isRecording,
      recordStartTime: this.data.recordStartTime,
      recordEndTime: this.data.recordEndTime,
      recordStartTimeFormatted: this.data.recordStartTimeFormatted,
      recordEndTimeFormatted: this.data.recordEndTimeFormatted,
      recordDurationText: this.data.recordDurationText,
      recordAreaStyle: this.data.recordAreaStyle,
      baseTime: this.data.baseTime,
      currentTime: this.data.currentTime,
      pausedTime: this.data.pausedTime,
      sliderValue: this.data.sliderValue,
      isPlaying: this.data.isPlaying,
    };

    // 按照用户需求重新实现录制区域时间计算逻辑
    const currentBaselineTime = this.data.currentTime;
    const scaleInterval = this._getScaleConfig(this.data.scaleUnit).interval;
    const realCurrentTime = Date.now();

    // 计算基准线 + 10个刻度后的时间
    const baselinePlus10Scales = currentBaselineTime + 10 * scaleInterval;

    let finalStartTime, finalEndTime;

    if (baselinePlus10Scales > realCurrentTime) {
      // 如果基准线+10个刻度超过了当前时间
      // 则以最新时间作为结束时间，开始时间以最新时间倒推10个刻度
      finalEndTime = realCurrentTime;
      finalStartTime = realCurrentTime - 10 * scaleInterval;
    } else {
      // 如果基准线+10个刻度没有超过当前时间
      // 则以基准线为开始时间，结束时间为基准线+10个刻度
      finalStartTime = currentBaselineTime;
      finalEndTime = baselinePlus10Scales;
    }

    // 停止播放并设置录制状态（保持在当前时间位置）
    this.stopAutoPlay();

    // 设置录制状态，保持当前时间不变
    this.setData({
      isRecording: true,
      isPlaying: false, // 暂停播放
      recordStartTime: finalStartTime,
      recordEndTime: finalEndTime,
      recordStartTimeFormatted: this.formatTime(finalStartTime),
      recordEndTimeFormatted: this.formatTime(finalEndTime),
      recordAreaAnimation: "record-area-enter",
    });

    // 重新生成刻度，确保基准线位置正确
    this.generateTimeScales();

    // 更新录制时长和显示 - 使用新的录制区域更新方法
    this._updateRecordDurationText();
    this._updateRecordAreaNew();

    // 清除动画
    setTimeout(() => this.setData({ recordAreaAnimation: "" }), 300);
  },

  /**
   * 全新的录制区域更新方法 - 纯数学计算，彻底解决绘制问题
   */
  _updateRecordAreaNew: function () {
    if (!this.data.isRecording) {
      this.setData({ recordAreaStyle: "display: none;" });
      return;
    }

    const {
      recordStartTime,
      recordEndTime,
      containerWidth,
      currentTime,
      baseTime,
      pixelsPerSecond,
    } = this.data;

    // 参数验证
    if (
      !recordStartTime ||
      !recordEndTime ||
      !containerWidth ||
      !pixelsPerSecond
    ) {
      return;
    }

    // 录制区域应该基于固定的时间范围，不跟随currentTime变化
    const centerPosition = containerWidth / 2;

    // 关键修复：录制区域位置应该相对于当前显示的时间轴中心计算
    // 但录制区域本身代表固定的时间范围，所以要用当前显示的centerTime
    const displayCenterTime = currentTime || baseTime;

    // 计算录制区域相对于当前显示中心的位置
    const startRelativeTime = (recordStartTime - displayCenterTime) / 1000;
    const endRelativeTime = (recordEndTime - displayCenterTime) / 1000;

    const startPosition = centerPosition + startRelativeTime * pixelsPerSecond;
    const endPosition = centerPosition + endRelativeTime * pixelsPerSecond;

    // 计算最终位置
    const leftPosition = Math.min(startPosition, endPosition);
    const rightPosition = Math.max(startPosition, endPosition);
    const width = Math.abs(rightPosition - leftPosition);

    // 确保最小宽度
    const finalWidth = Math.max(width, 60);
    const finalLeft = leftPosition;

    // 构建样式
    const recordAreaStyle = `
      display: block;
      left: ${finalLeft}px;
      width: ${finalWidth}px;
      position: absolute;
      height: 100rpx;
      background: linear-gradient(90deg, rgba(76, 175, 80, 0.3) 0%, rgba(139, 195, 74, 0.25) 50%, rgba(76, 175, 80, 0.3) 100%);
      border: 2rpx solid rgba(76, 175, 80, 0.6);
      box-shadow: 0 0 8rpx rgba(76, 175, 80, 0.3);
      min-width: 60rpx;
      box-sizing: border-box;
      transition: none !important;
    `
      .replace(/\s+/g, " ")
      .trim();

    this.setData({ recordAreaStyle });
  },

  /**
   * 检测单个项目的变化
   * @param {Object} currentItem 当前项目
   * @param {Object} newItem 新项目
   * @returns {Object} 变化检测结果
   */
  _detectItemChanges(currentItem, newItem) {
    const result = {
      hasChange: false,
      changeType: null,
    };

    const keyFields = ["sliceStatue", "coverKey", "objectKey"];

    for (const field of keyFields) {
      if (currentItem[field] !== newItem[field]) {
        result.hasChange = true;

        if (field === "sliceStatue") {
          result.changeType = this._getStatusChangeType(
            currentItem.sliceStatue,
            newItem.sliceStatue,
          );
        } else if (field === "coverKey") {
          result.changeType = "thumbnail";
          // 强制刷新缩略图，避免缓存问题
          newItem.thumbnail = this._processThumbnailUrl(newItem.coverKey, true);
        }
        break;
      }
    }

    // 特殊处理：切片成功状态
    if (newItem.sliceStatue === 20 && currentItem.sliceStatue !== 20) {
      result.hasChange = true;
      result.changeType = "success";
      // 切片成功时重新处理缩略图
      newItem.thumbnail = this._processThumbnailUrl(newItem.coverKey, true);
    }

    return result;
  },

  _easeOutCubic: function (t) {
    return 1 - Math.pow(1 - t, 3);
  },

  // 切换摄像头
  /**
   * 扩展时间刻度（无限预加载的核心方法）
   * @param {boolean} extendLeft 是否向左扩展
   * @param {boolean} extendRight 是否向右扩展
   * @param {number} currentOffset 当前偏移量
   */
  _extendTimeScales: function (extendLeft, extendRight, currentOffset) {
    const {
      baseTime,
      pixelsPerSecond,
      containerWidth,
      scaleUnit,
      timeScales,
      leftBoundaryTime,
    } = this.data;

    if (!baseTime || !pixelsPerSecond || !containerWidth) return;

    const scaleConfig = this._getScaleConfig(scaleUnit);
    const interval = scaleConfig.interval;
    const realCurrentTime = Date.now();

    let newScales = [...timeScales];
    const extensionRange = containerWidth * 1.5; // 每次扩展1.5个屏幕宽度的刻度

    try {
      const originalScaleCount = newScales.length;
      let actualLeftExtended = false;
      let actualRightExtended = false;

      // 向左扩展（过去时间）- 新增边界限制
      if (extendLeft && timeScales.length > 0) {
        // 安全地获取最小时间，避免只读错误
        let leftmostTime = Infinity;
        for (let i = 0; i < timeScales.length; i++) {
          if (timeScales[i] && typeof timeScales[i].time === "number") {
            leftmostTime = Math.min(leftmostTime, timeScales[i].time);
          }
        }

        // 检查是否已经到达边界，如果是则跳过扩展
        if (leftBoundaryTime && leftmostTime <= leftBoundaryTime + interval) {
        } else {
          let extensionStartTime =
            leftmostTime - (extensionRange / pixelsPerSecond) * 1000;

          // 应用左边界限制：不允许扩展到leftBoundaryTime之前
          if (leftBoundaryTime && extensionStartTime < leftBoundaryTime) {
            extensionStartTime = leftBoundaryTime;
          }

          for (
            let time = leftmostTime - interval;
            time >= extensionStartTime;
            time -= interval
          ) {
            // 再次检查边界，确保不会生成早于leftBoundaryTime的刻度
            if (leftBoundaryTime && time < leftBoundaryTime) {
              break;
            }

            const position = this._calculateAlignedPosition(
              time,
              baseTime,
              pixelsPerSecond,
              containerWidth,
              currentOffset,
            );
            const date = new Date(time);
            const scaleInfo = this._getScaleInfo(date, scaleUnit);

            let timeText = "";
            if (scaleInfo.showText) {
              timeText = this._formatTimeText(date, scaleUnit);
            }

            const isFuture = time > realCurrentTime;

            newScales.unshift({
              time,
              position,
              scaleType: scaleInfo.type,
              timeText,
              isFuture,
            });

            actualLeftExtended = true;
          }
        }
      }

      // 向右扩展（未来时间）
      if (extendRight && timeScales.length > 0) {
        // 安全地获取最大时间，避免只读错误
        let rightmostTime = -Infinity;
        for (let i = 0; i < timeScales.length; i++) {
          if (timeScales[i] && typeof timeScales[i].time === "number") {
            rightmostTime = Math.max(rightmostTime, timeScales[i].time);
          }
        }
        const extensionEndTime =
          rightmostTime + (extensionRange / pixelsPerSecond) * 1000;

        for (
          let time = rightmostTime + interval;
          time <= extensionEndTime;
          time += interval
        ) {
          const position = this._calculateAlignedPosition(
            time,
            baseTime,
            pixelsPerSecond,
            containerWidth,
            currentOffset,
          );
          const date = new Date(time);
          const scaleInfo = this._getScaleInfo(date, scaleUnit);

          let timeText = "";
          if (scaleInfo.showText) {
            timeText = this._formatTimeText(date, scaleUnit);
          }

          const isFuture = time > realCurrentTime;

          newScales.push({
            time,
            position,
            scaleType: scaleInfo.type,
            timeText,
            isFuture,
          });

          actualRightExtended = true;
        }
      }

      // 暂时禁用去重处理，避免只读错误
      // try {
      //   newScales = this._deduplicateScales(newScales);
      // } catch (error) {
      //   console.error("刻度去重失败:", error);
      // }

      // 检查是否实际生成了新刻度
      const finalScaleCount = newScales.length;
      const actuallyExtended = finalScaleCount > originalScaleCount;

      // 限制刻度总数，避免内存过度使用
      const maxScales = 500; // 最大刻度数量
      if (newScales.length > maxScales) {
        // 保留中间部分的刻度，创建新数组避免修改原数组
        const centerIndex = Math.floor(newScales.length / 2);
        const halfMax = Math.floor(maxScales / 2);
        const trimmedScales = newScales.slice(
          centerIndex - halfMax,
          centerIndex + halfMax,
        );
        newScales = trimmedScales;
      }

      // 只有在实际扩展了刻度时才更新数据和打印日志
      if (actuallyExtended) {
        // 更新刻度数据
        this.setData({
          timeScales: newScales,
        });
      }
    } catch (error) {
      console.error("扩展刻度失败:", error);
      // 扩展失败时回退到完整重新生成
      this.generateTimeScales();
    }
  },

  /**
   * 刻度去重处理 - 简化版本，避免只读错误
   * @param {Array} scales 刻度数组
   * @returns {Array} 去重后的刻度数组
   */
  _deduplicateScales: function (scales) {
    if (!scales || scales.length === 0) return [];

    try {
      const uniqueScales = [];
      const seenTimes = new Set();

      // 简单的时间去重，避免复杂操作
      for (let i = 0; i < scales.length; i++) {
        const scale = scales[i];

        // 检查基本数据有效性
        if (!scale || typeof scale.time !== "number" || isNaN(scale.time)) {
          continue;
        }

        // 时间去重（精确匹配）
        if (!seenTimes.has(scale.time)) {
          seenTimes.add(scale.time);

          // 创建新对象，避免修改只读对象
          uniqueScales.push({
            time: scale.time,
            position: scale.position,
            scaleType: scale.scaleType,
            timeText: scale.timeText || "",
            isFuture: scale.isFuture || false,
          });
        }
      }

      return uniqueScales;
    } catch (error) {
      console.error("刻度去重失败:", error);
      // 如果失败，直接返回原数组
      return scales || [];
    }
  },

  /**
   * 验证和清理刻度数据 - 确保刻度数据的完整性和正确性
   * @param {Array} scales 刻度数组
   * @returns {Array} 验证后的刻度数组
   */
  _validateAndCleanScales: function (scales) {
    if (!scales || scales.length === 0) return [];

    try {
      const validScales = [];
      const { containerWidth } = this.data;

      for (let i = 0; i < scales.length; i++) {
        const scale = scales[i];

        // 验证刻度数据的完整性
        if (
          !scale ||
          typeof scale.time !== "number" ||
          typeof scale.position !== "number" ||
          !scale.scaleType ||
          isNaN(scale.time) ||
          isNaN(scale.position)
        ) {
          continue; // 跳过无效数据，不打印警告避免刷屏
        }

        // 验证位置是否在合理范围内（允许一定的缓冲区）
        if (
          scale.position < -containerWidth * 2 ||
          scale.position > containerWidth * 3
        ) {
          continue; // 跳过位置过于极端的刻度
        }

        // 验证时间是否合理（不能是未来太远的时间）
        const now = Date.now();
        if (scale.time > now + 86400000) {
          // 不能超过当前时间24小时
          continue;
        }

        // 创建刻度副本，避免修改只读对象
        validScales.push({
          time: scale.time,
          position: scale.position,
          scaleType: scale.scaleType,
          timeText: scale.timeText || "",
          isFuture: scale.isFuture || false,
        });
      }

      // 按时间排序，确保刻度顺序正确
      validScales.sort((a, b) => a.time - b.time);

      return validScales;
    } catch (error) {
      console.error("刻度验证失败，返回原数组:", error);
      // 如果验证失败，返回原数组的副本
      return scales.slice();
    }
  },

  _finalizeTrackPositionSync: function (finalTime) {
    // 使用传入的最终时间
    const syncTime = finalTime || this.data.currentTime;

    // 直接同步所有时间相关状态
    // 录制区域的时间保持绝对不变，让其自然回到对应的时间位置
    const sliderValue = this._timeToSliderValue(syncTime);
    this.setData({
      baseTime: syncTime, // 基准时间
      currentTime: syncTime, // 当前时间
      pausedTime: syncTime, // 暂停时间
      isDragging: false,
      isPlaying: false,
      sliderValue: sliderValue, // 更新slider值
      formattedCurrentTime: this.formatTime(syncTime),
    });

    // 重新生成刻度 - 但要避免录制区域闪烁
    // 先检查录制区域是否在新的baseTime下可见，如果不在则先隐藏
    if (this.data.isRecording) {
      const isVisible = this._isRecordAreaVisibleAtTime(syncTime);
      if (!isVisible) {
        this.setData({ recordAreaStyle: "display: none;" });
      }
    }

    this.generateTimeScales();
  },
  // 基于DOM位置查找最接近的刻度 - 专门用于录制区域绘制
  _findClosestScaleForDOM: function (targetTime) {
    const { timeScales } = this.data;

    if (!timeScales.length) return null;

    // 方法1：如果能找到完全匹配的刻度，直接返回
    for (let scale of timeScales) {
      if (scale.time === targetTime) {
        return {
          time: scale.time,
          position: scale.position,
          scaleType: scale.scaleType,
          domBased: true, // 标记为基于DOM的刻度
        };
      }
    }

    // 方法2：找到最接近的两个刻度，基于DOM位置进行插值计算
    let beforeScale = null;
    let afterScale = null;

    for (let scale of timeScales) {
      if (scale.time <= targetTime) {
        if (!beforeScale || scale.time > beforeScale.time) {
          beforeScale = scale;
        }
      }
      if (scale.time >= targetTime) {
        if (!afterScale || scale.time < afterScale.time) {
          afterScale = scale;
        }
      }
    }

    // 如果找到了前后两个刻度，基于DOM位置进行插值计算
    if (beforeScale && afterScale && beforeScale.time !== afterScale.time) {
      const ratio =
        (targetTime - beforeScale.time) / (afterScale.time - beforeScale.time);
      const interpolatedPosition =
        beforeScale.position +
        (afterScale.position - beforeScale.position) * ratio;

      return {
        time: targetTime,
        position: interpolatedPosition,
        scaleType: "interpolated",
        domBased: true, // 标记为基于DOM的刻度
      };
    }

    // 方法3：如果只有一个刻度，返回最接近的刻度
    const closestScale = beforeScale || afterScale;
    if (closestScale) {
      return {
        time: targetTime,
        position: closestScale.position,
        scaleType: closestScale.scaleType,
        domBased: true, // 标记为基于DOM的刻度
      };
    }

    return null;
  },

  /**
   * 查找最接近目标时间的刻度
   * 使用多种策略查找刻度：完全匹配、插值计算、位置计算
   * @param {number} targetTime 目标时间戳
   * @returns {Object|null} 刻度对象，包含时间、位置和类型信息
   */
  _findClosestScale: function (targetTime) {
    const {
      timeScales,
      pixelsPerSecond,
      baseTime,
      containerWidth,
      trackOffset,
    } = this.data;

    // 检查刻度数组是否为空
    if (!timeScales.length) return null;

    // 方法1：查找完全匹配的刻度
    for (let scale of timeScales) {
      if (scale.time === targetTime) {
        return scale;
      }
    }

    // 方法2：找到最接近的前后两个刻度，进行插值计算
    let beforeScale = null;
    let afterScale = null;

    for (let scale of timeScales) {
      if (scale.time <= targetTime) {
        if (!beforeScale || scale.time > beforeScale.time) {
          beforeScale = scale;
        }
      }
      if (scale.time >= targetTime) {
        if (!afterScale || scale.time < afterScale.time) {
          afterScale = scale;
        }
      }
    }

    // 如果找到了前后两个刻度，进行插值计算
    if (beforeScale && afterScale && beforeScale.time !== afterScale.time) {
      const ratio =
        (targetTime - beforeScale.time) / (afterScale.time - beforeScale.time);
      const interpolatedPosition =
        beforeScale.position +
        (afterScale.position - beforeScale.position) * ratio;

      return {
        time: targetTime,
        position: interpolatedPosition,
        scaleType: "interpolated",
      };
    }

    // 方法3：如果只有一个刻度或无法插值，使用计算位置
    // 使用与刻度生成相同的计算逻辑
    let currentBaseTime = baseTime;
    if (!this.data.isPlaying) {
      currentBaseTime = this._alignToSecond(baseTime);
    }

    // 使用精确对齐的位置计算
    const calculatedPosition = this._calculateAlignedPosition(
      targetTime,
      currentBaseTime,
      pixelsPerSecond,
      containerWidth,
      trackOffset,
    );

    return {
      time: targetTime,
      position: calculatedPosition,
      scaleType: "calculated",
    };
  },
  // 摄像头列表
  /**
   * 格式化秒数为日期时间显示 (MM-DD xx:xx:xx 格式)
   * @param {number} seconds 秒数时间戳
   * @returns {string} 格式化后的时间字符串
   */
  _formatSecondsToTime(seconds) {
    if (!seconds) return "01-01 00:00:00";

    const date = new Date(seconds * 1000);
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const day = date.getDate().toString().padStart(2, "0");
    const hours = date.getHours().toString().padStart(2, "0");
    const minutes = date.getMinutes().toString().padStart(2, "0");
    const secs = date.getSeconds().toString().padStart(2, "0");

    return `${month}-${day} ${hours}:${minutes}:${secs}`;
  },

  /**
   * 格式化秒数为时长显示 (HH:MM:SS 格式)
   * @param {number} seconds 秒数
   * @returns {string} 格式化后的时长字符串
   */
  _formatSecondsToDuration(seconds) {
    if (!seconds || seconds <= 0) return "00:00:00";

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = Math.floor(seconds % 60);

    const pad = (n) => n.toString().padStart(2, "0");
    return `${pad(hours)}:${pad(minutes)}:${pad(remainingSeconds)}`;
  },
  /**
   * 格式化时间文本
   * 根据刻度单位格式化时间显示，支持秒级、分钟级和10分钟级刻度
   * @param {Date} date 日期对象
   * @param {string} scaleUnit 刻度单位 ('second', 'minute', '10minute')
   * @returns {string} 格式化后的时间字符串
   */
  _formatTimeText: function (date, scaleUnit) {
    const hours = date.getHours();
    const minutes = date.getMinutes();
    const seconds = date.getSeconds();

    switch (scaleUnit) {
      case "second":
        // 秒刻度：显示完整时间 HH:MM:SS
        return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;

      case "minute":
        // 分钟刻度：显示 HH:MM
        return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}`;

      case "10minute":
        // 10分钟刻度：显示 XX点
        return `${hours}点`;

      default:
        // 默认显示完整时间
        return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
    }
  },
  /**
   * 生成惯性滑动的关键帧
   * 根据初始速度生成指数衰减的动画关键帧，支持时间边界限制
   * @param {number} initialVelocity 初始速度（像素/秒）
   * @returns {Array} 关键帧数组，包含时间、位移、速度等信息
   */
  _generateInertiaKeyframes: function (initialVelocity) {
    const keyframes = [];
    const startCurrentTime = this.data.currentTime;
    const realCurrentTime = Date.now();
    const { leftBoundaryTime } = this.data;

    // 动画参数 - 调整为更短的时长和更大的阻力
    const duration = 400; // 从750ms减少到400ms，缩短滑动时间
    const frameInterval = 16; // 16ms间隔生成关键帧

    // 计算总帧数
    const totalFrames = Math.ceil(duration / frameInterval);

    // 使用指数衰减函数计算每帧的位移
    // 增加阻力，让滑动更快停下来
    const decayFactor = 0.92; // 从0.98降低到0.92，增加阻力
    let totalDisplacement = 0;

    // 速度衰减因子，进一步降低初始速度
    const velocityDamping = 0.6; // 将初始速度降低到60%
    const dampedInitialVelocity = initialVelocity * velocityDamping;

    for (let frame = 0; frame < totalFrames; frame++) {
      const time = frame * frameInterval;
      const progress = frame / totalFrames;

      // 使用指数衰减函数：velocity = dampedInitialVelocity * (decayFactor ^ frame)
      const currentVelocity =
        dampedInitialVelocity * Math.pow(decayFactor, frame);

      // 计算这一帧的位移
      const displacement = currentVelocity * frameInterval;
      totalDisplacement += displacement;

      const timeOffset =
        (-totalDisplacement / this.data.pixelsPerSecond) * 1000;
      const currentTime = startCurrentTime + timeOffset;

      // 时间限制检查：如果超过当前真实时间或左边界时间，截断动画
      if (currentTime > realCurrentTime) {
        // 计算到达当前时间的精确位移
        const timeToLimit = realCurrentTime - startCurrentTime;
        const limitDisplacement =
          (-timeToLimit / 1000) * this.data.pixelsPerSecond;

        keyframes.push({
          time: time,
          currentTime: realCurrentTime,
          displacement: limitDisplacement,
          velocity: currentVelocity,
          reachedTimeLimit: true, // 标记达到时间限制
        });

        // 截断动画，不再生成后续关键帧
        break;
      } else if (leftBoundaryTime && currentTime < leftBoundaryTime) {
        // 计算到达左边界时间的精确位移
        const timeToLimit = leftBoundaryTime - startCurrentTime;
        const limitDisplacement =
          (-timeToLimit / 1000) * this.data.pixelsPerSecond;

        keyframes.push({
          time: time,
          currentTime: leftBoundaryTime,
          displacement: limitDisplacement,
          velocity: currentVelocity,
          reachedLeftBoundary: true, // 标记达到左边界限制
        });

        // 截断动画，不再生成后续关键帧
        break;
      }

      keyframes.push({
        time: time,
        currentTime: currentTime,
        displacement: totalDisplacement,
        velocity: currentVelocity,
      });

      // 提高速度阈值，让动画更早结束
      if (Math.abs(currentVelocity) < 0.05) {
        // 从0.01提高到0.05
        break;
      }
    }

    return keyframes;
  },
  /**
   * 获取缓冲区倍数
   * 根据刻度单位返回相应的缓冲区倍数，确保播放时有足够的刻度预加载
   * @param {string} scaleUnit 刻度单位
   * @returns {number} 缓冲区倍数
   */
  _getBufferMultiplier: function (scaleUnit) {
    // 大幅增加缓冲区倍数，确保播放时有足够的刻度预加载
    const multipliers = {
      second: 20, // 从12增加到20
      minute: 18, // 从10增加到18
      "10minute": 15, // 从10增加到15
    };
    return multipliers[scaleUnit] || 20;
  },
  /**
   * 动态获取容器宽度
   * 获取系统信息和容器实际宽度，支持高DPI设备的像素对齐
   * @returns {Promise<void>} 获取完成的Promise
   */
  _getContainerWidth: function () {
    return new Promise((resolve) => {
      // 获取系统信息
      const systemInfo = wx.getSystemInfoSync();
      const screenWidth = systemInfo.screenWidth;
      const pixelRatio = systemInfo.pixelRatio || 1;

      // 使用选择器查询获取实际容器宽度
      const query = wx.createSelectorQuery().in(this);
      query
        .select(".slider-track-container")
        .boundingClientRect((rect) => {
          if (rect && rect.width > 0) {
            // 对容器宽度进行像素对齐，确保在高DPI设备上精确对齐
            const alignedWidth =
              Math.round(rect.width * pixelRatio) / pixelRatio;

            this.setData({
              containerWidth: alignedWidth,
              pixelRatio: pixelRatio,
              deviceType: this._getDeviceType(screenWidth),
            });
          } else {
            // 获取失败时使用屏幕宽度作为备选方案
            const alignedScreenWidth =
              Math.round(screenWidth * pixelRatio) / pixelRatio;
            this.setData({
              containerWidth: alignedScreenWidth,
              pixelRatio: pixelRatio,
              deviceType: this._getDeviceType(screenWidth),
            });
          }
          resolve();
        })
        .exec();
    });
  },
  /**
   * 获取设备类型
   * 根据屏幕宽度判断设备类型，用于适配不同屏幕尺寸
   * @param {number} screenWidth 屏幕宽度
   * @returns {string} 设备类型 ('tablet', 'large-phone', 'medium-phone', 'small-phone')
   */
  _getDeviceType: function (screenWidth) {
    if (screenWidth >= 768) {
      return "tablet"; // 平板
    } else if (screenWidth >= 414) {
      return "large-phone"; // 大屏手机
    } else if (screenWidth >= 375) {
      return "medium-phone"; // 中等手机
    } else {
      return "small-phone"; // 小屏手机
    }
  },
  /**
   * 获取刻度配置
   * 根据刻度单位返回相应的配置信息，包括间隔、像素宽度等
   * @param {string} scaleUnit 刻度单位 ('second', 'minute', '10minute')
   * @returns {Object} 刻度配置对象
   */
  _getScaleConfig: function (scaleUnit) {
    const configs = {
      second: {
        interval: 1000, // 1秒间隔
        majorInterval: 60000, // 1分钟主刻度
        minorInterval: 10000, // 10秒次刻度
        basePixelWidth: 10, // 基础像素宽度
        scaleMultiplier: 1.0, // 刻度间隔倍数
      },
      minute: {
        interval: 60000, // 1分钟间隔
        majorInterval: 300000, // 5分钟主刻度
        minorInterval: 180000, // 3分钟次刻度
        basePixelWidth: 12, // 稍微宽一点
        scaleMultiplier: 1.2,
      },

      "10minute": {
        interval: 600000, // 10分钟间隔
        majorInterval: 3600000, // 1小时主刻度
        minorInterval: 1800000, // 30分钟次刻度
        basePixelWidth: 18, // 最宽
        scaleMultiplier: 1.8,
      },
    };
    return configs[scaleUnit] || configs["second"];
  },

  /**
   * 根据刻度单位获取录制区域的最短时间限制 - 基于10个刻度的距离
   * @param {string} scaleUnit 刻度单位
   * @returns {number} 最短时间限制（毫秒）
   */
  _getMinRecordDuration: function (scaleUnit) {
    const scaleConfig = this._getScaleConfig(scaleUnit);
    const scaleInterval = scaleConfig.interval; // 单个刻度的时间间隔（毫秒）

    // 始终保持10个刻度的距离
    return 10 * scaleInterval;
  },
  /**
   * 获取刻度信息
   * 根据时间和刻度单位判断刻度类型和是否显示文本
   * @param {Date} date 日期对象
   * @param {string} scaleUnit 刻度单位
   * @returns {Object} 包含type和showText的刻度信息对象
   */
  _getScaleInfo: function (date, scaleUnit) {
    const minutes = date.getMinutes();
    const seconds = date.getSeconds();
    const hours = date.getHours();

    switch (scaleUnit) {
      case "second":
        // 秒为单位：每十秒用高刻度并显示时间标签，其他为普通刻度
        if (seconds % 10 === 0) {
          return { type: "high", showText: true }; // 每10秒高刻度，显示时间标签
        } else {
          return { type: "normal", showText: false }; // 每秒普通刻度
        }

      case "minute":
        // 分钟为单位：每十分钟用高刻度并显示时间标签，其他为普通刻度
        if (minutes % 10 === 0) {
          return { type: "high", showText: true }; // 每10分钟高刻度，显示时间标签
        } else {
          return { type: "normal", showText: false }; // 每分钟普通刻度
        }

      case "10minute":
        // 10分钟为单位：每整小时用高刻度并显示时间标签，其他为普通刻度
        if (minutes === 0) {
          return { type: "high", showText: true }; // 每小时高刻度，显示时间标签
        } else {
          return { type: "normal", showText: false }; // 每10分钟普通刻度
        }

      default:
        return { type: "normal", showText: false };
    }
  },
  // 开始剪辑
  /**
   * 获取状态变化类型
   * @param {number} oldStatus 旧状态
   * @param {number} newStatus 新状态
   * @returns {string|null} 状态变化类型
   */
  _getStatusChangeType(oldStatus, newStatus) {
    if (newStatus === 20 && oldStatus !== 20) {
      return "success";
    } else if (newStatus === 40 && oldStatus !== 40) {
      return "failed";
    }
    return null;
  },
  // 结束剪辑
  _handleDragEnd: function () {
    // 结束拖拽状态
    this.setData({ isDragging: false });

    // 清除视频预览定时器
    if (this._videoPreviewTimer) {
      clearTimeout(this._videoPreviewTimer);
      this._videoPreviewTimer = null;
    }

    // 检查当前时间边界
    const realCurrentTime = Date.now();
    const currentTime = this.data.currentTime;
    const { leftBoundaryTime } = this.data;

    if (currentTime > realCurrentTime) {
      // 超过当前时间，同步到当前时间并开始播放
      const sliderValue = this._timeToSliderValue(realCurrentTime);
      this.setData({
        currentTime: realCurrentTime,
        baseTime: realCurrentTime,
        pausedTime: 0, // 重置暂停时间，允许实时播放
        sliderValue: sliderValue,
        isPlaying: true,
        formattedCurrentTime: this.formatTime(realCurrentTime),
      });
      this.generateTimeScales();
      this.startAutoPlay();
    } else if (leftBoundaryTime && currentTime < leftBoundaryTime) {
      // 超过左边界，同步到左边界
      const sliderValue = this._timeToSliderValue(leftBoundaryTime);
      this.setData({
        currentTime: leftBoundaryTime,
        baseTime: leftBoundaryTime,
        pausedTime: leftBoundaryTime, // 设置暂停时间为边界时间
        sliderValue: sliderValue,
        formattedCurrentTime: this.formatTime(leftBoundaryTime),
      });
      this.generateTimeScales();
      wx.showToast({
        title: "已到达最早可查看时间",
        icon: "none",
        duration: 1500,
      });
    } else {
      // 确保 pausedTime 被正确设置为当前时间
      this.setData({
        pausedTime: currentTime,
      });

      // 计算拖拽速度，如果有足够速度则启动惯性动画
      const velocity = this._calculateVelocity();

      if (Math.abs(velocity) > 0.3) {
        // 启动简化的惯性动画
        this._startSimpleInertiaAnimation(velocity);
      } else {
        // 速度不够，直接更新视频
        this._updateVideoUrlAfterDrag(currentTime);
      }
    }

    // 清理速度追踪和拖拽相关状态
    if (this.velocityTracker) {
      this.velocityTracker = null;
    }

    // 清理轨道拖拽追踪器，避免影响后续的缩放操作
    if (this._trackDragTracker) {
      this._trackDragTracker = null;
    }

    // 清理拖拽追踪器
    if (this._dragTracker) {
      this._dragTracker = null;
    }

    // 清理格式缓存
    this._trackFormatCache = null;
    this._cachedBaseData = null;

    // 重置拖拽相关的待处理标志
    this._recordAreaUpdatePending = false;
    this._trackComplexUpdatePending = false;
    this._formatUpdatePending = false;
  },
  // 未上线功能
  _handleDragMove: function (e) {
    // 如果正在拖拽录制区域，不处理轨道拖拽
    if (this.data.isRecordAreaDragging) return;

    const currentX = e.touches[0].clientX;
    const currentTime = Date.now();

    // 更新速度追踪
    if (this.velocityTracker) {
      this.velocityTracker.positions.push({ x: currentX, time: currentTime });
      // 只保留最近的采样点
      if (
        this.velocityTracker.positions.length > this.velocityTracker.maxSamples
      ) {
        this.velocityTracker.positions.shift();
      }
    }

    // 计算拖拽距离
    const dragDistance = currentX - this.data.dragStartX;

    // 计算新的时间（向左拖动时间增加，向右拖动时间减少）
    const timeOffset = (-dragDistance / this.data.pixelsPerSecond) * 1000;
    const newCurrentTime = this.data.dragStartTime + timeOffset;

    // 使用简化的时间更新，适配slider系统
    this._updateTimeForDrag(newCurrentTime);
  },
  // 重置录制区域
  _handleDragStart: function (e) {
    // 如果正在拖拽录制区域，不处理轨道拖拽
    if (this.data.isRecordAreaDragging) return;

    // 停止自动播放和惯性动画
    this.stopAutoPlay();
    this._stopInertiaAnimation();

    // 清除视频预览定时器
    if (this._videoPreviewTimer) {
      clearTimeout(this._videoPreviewTimer);
      this._videoPreviewTimer = null;
    }

    const touchX = e.touches[0].clientX;
    const currentTime = Date.now();

    // 初始化轨道拖拽追踪器
    this._trackDragTracker = null;
    this._trackFormatCache = null;
    this._recordAreaUpdatePending = false;
    this._trackComplexUpdatePending = false;

    // 设置拖拽状态
    this.setData({
      isPlaying: false,
      isDragging: true,
      pausedTime: this.data.currentTime,
      dragStartX: touchX,
      dragStartTime: this.data.currentTime,
    });

    // 初始化速度追踪
    this.velocityTracker = {
      positions: [{ x: touchX, time: currentTime }],
      maxSamples: 5, // 保留最近5个采样点
    };
  },
  // 格式化时间为 HH:MM:SS 格式
  /**
   * 处理录像列表错误（优化版）
   * @param {Error} error 错误对象
   * @returns {Array} 当前录像列表
   */
  _handleVideoListError(error) {
    console.error("获取录像列表请求失败:", error);

    // 根据错误类型进行不同处理
    if (error.message && error.message.includes("request:fail")) {
      console.warn("网络请求失败，保留当前列表");
      // 网络错误时保留当前列表
      return this.data.videoList || [];
    } else if (error.code === 401 || error.code === 403) {
      console.warn("权限错误，清空列表");
      // 权限错误时清空列表
      this.setData({ videoList: [] });
      return [];
    } else {
      console.warn("其他错误，保留当前列表");
      // 其他错误保留当前列表
      return this.data.videoList || [];
    }
  },
  // 智能格式化时长 - 根据时长长度显示不同格式，支持毫秒和秒数输入
  /**
   * 处理录像列表响应数据（优化版）
   * @param {Object} res API响应数据
   * @returns {Array} 处理后的录像列表
   */
  _handleVideoListResponseOptimized(res) {
    if (res.code === 0 && res.data && res.data.list) {
      const newRawList = res.data.list;
      const newProcessedList = this._processVideoListData(newRawList);
      const newSortedList = this._sortVideoList(newProcessedList);

      this._performDifferentialUpdate(newSortedList);
      return newSortedList;
    } else {
      console.warn("获取录像列表失败:", res.msg);
      this.setData({ videoList: [] });
      return [];
    }
  },

  // 生成时间刻度 - 优化基准线对齐和拖拽缓冲
  async _initClipping() {
    try {
      // 初始化时间，确保对齐到整秒
      const initialTime = this._alignToSecond(Date.now());
      this.setData({
        baseTime: initialTime,
        currentTime: initialTime,
        isPlaying: false, // 修复：页面初始化时不自动播放，只有进入剪辑状态才播放
      });

      // 初始化slider数据
      this._initSliderData();

      // 动态获取容器宽度，但不立即生成刻度和开始播放
      await this._getContainerWidth();
      this.updateFormattedTime();

      // 初始化缩放按钮状态
      this._updateZoomButtonStatus();
      // 移除自动播放和刻度生成，只有进入剪辑状态时才启动

      // 启动刻度颜色更新定时器
      this._startScaleColorTimer();
    } catch (error) {
      console.error("剪辑初始化失败:", error);
      // 设置默认值，确保页面能正常显示
      this.setData({
        baseTime: Date.now(),
        currentTime: Date.now(),
        isPlaying: false,
        containerWidth: 375, // 设置默认容器宽度
      });
    }
  },
  // 获取刻度配置 - 动态间隔设置
  async _initPageData(options) {
    const { id } = options;
    try {
      // 获取站点详情
      const siteDetailRes = await querySiteDetailApi({ id });
      this.setData({
        siteDetail: siteDetailRes.data,
        shareUrl: `/play-live/living-mult-v2/index?pCode=${app.globalData.user.userCode}&id=${id}`,
        navData: {
          title: siteDetailRes.data.siteName,
          color: "#ffffff",
        },
      });
    } catch (e) {
      this.errorAndReturn();
    }
  },
  // 获取缓冲倍数
  /**
   * 检查列表是否相同
   * @param {Array} currentList 当前列表
   * @param {Array} newList 新列表
   * @returns {boolean} 是否相同
   */
  _isListIdentical(currentList, newList) {
    if (currentList.length !== newList.length) {
      return false;
    }

    for (let i = 0; i < currentList.length; i++) {
      const current = currentList[i];
      const newItem = newList.find((item) => item.id === current.id);

      if (!newItem) {
        return false;
      }

      // 检查关键字段是否有变化
      const keyFields = [
        "sliceStatue",
        "coverKey",
        "objectKey",
        "startTime",
        "endTime",
      ];

      for (const field of keyFields) {
        if (current[field] !== newItem[field]) {
          return false;
        }
      }
    }

    return true;
  },
  // 获取刻度信息 - 简化为两种刻度状态：普通和高刻度
  // 格式化时间文本 - 统一为 XX:XX:XX 格式
  _isRecordAreaVisibleAtTime: function (baseTime) {
    const { recordStartTime, recordEndTime, pixelsPerSecond, containerWidth } =
      this.data;

    if (!recordStartTime || !recordEndTime) {
      return false;
    }

    // 计算录制区域在指定baseTime下的位置
    const centerPosition = containerWidth / 2;
    const startRelativeTime = recordStartTime - baseTime;
    const endRelativeTime = recordEndTime - baseTime;

    const startPosition =
      centerPosition + (startRelativeTime / 1000) * pixelsPerSecond;
    const endPosition =
      centerPosition + (endRelativeTime / 1000) * pixelsPerSecond;

    const leftmost = Math.min(startPosition, endPosition);
    const rightmost = Math.max(startPosition, endPosition);

    // 检查是否与可视区域有交集
    return !(rightmost < 0 || leftmost > containerWidth);
  },
  // 统一的录制区域更新方法 - 基于DOM刻度重构
  _isTimeInRecordArea: function (time) {
    if (
      !this.data.isRecording ||
      !this.data.recordStartTime ||
      !this.data.recordEndTime
    ) {
      return false;
    }

    const startTime = Math.min(
      this.data.recordStartTime,
      this.data.recordEndTime,
    );
    const endTime = Math.max(
      this.data.recordStartTime,
      this.data.recordEndTime,
    );

    return time >= startTime && time <= endTime;
  },

  /**
   * 跳转到最新时间 - 重新实现，专注于轨道播放而不依赖视频状态
   */
  jumpToLatestTime: function () {
    // 停止当前播放
    this.stopAutoPlay();

    // 获取当前真实时间
    const currentRealTime = Date.now();

    // 计算slider值
    const sliderValue = this._timeToSliderValue(currentRealTime);

    // 获取当前激活摄像头的直播链接
    const cameraIndex = this.data.activateCameraIndex;
    const cameraList = this.data.cameraList;
    let liveUrl = "";

    if (cameraList && cameraIndex >= 0 && cameraIndex < cameraList.length) {
      liveUrl = cameraList[cameraIndex].liveUrl;
    }

    // 跳转到最新时间，设置为播放状态
    this.setData({
      isPlaying: false, // 先设置为暂停状态，避免startAutoPlay中的逻辑冲突
      baseTime: currentRealTime,
      currentTime: currentRealTime,
      pausedTime: currentRealTime, // 设置为当前时间，让startAutoPlay从这个时间开始
      sliderValue: sliderValue,
      formattedCurrentTime: this.formatTime(currentRealTime),
      // 更新slider时间范围到最新
      timeRangeEnd: currentRealTime,
      // 设置视频URL为直播链接
      videoUrl: liveUrl,
    });

    // 重新生成刻度
    this.generateTimeScales();

    // 更新录制区域（如果存在）
    if (this.data.isRecording) {
      this._updateRecordAreaNew();
    }

    // 播放视频
    this.playVideo();

    // 确保状态更新完成后再启动自动播放
    wx.nextTick(() => {
      setTimeout(() => {
        // 检查是否还在当前页面且没有被其他操作中断
        if (this.data.currentTime === currentRealTime) {
          this.setData({ isPlaying: true });
          this.startAutoPlay();
        }
      }, 50);
    });

    // 显示提示
    wx.showToast({
      title: "已跳转到最新时间",
      icon: "success",
      duration: 1500,
    });
  },

  /**
   * 跳转到最左侧边界时间
   */
  jumpToStartTime: function () {
    // 停止当前播放
    this.stopAutoPlay();

    // 始终跳转到最左侧边界时间，不管是否有录制区域
    const targetTime =
      this.data.leftBoundaryTime || Date.now() - 23 * 60 * 60 * 1000;

    // 计算slider值
    const sliderValue = this._timeToSliderValue(targetTime);

    // 跳转到目标时间，先设置为暂停状态
    this.setData({
      isPlaying: false, // 先设置为暂停状态，避免startAutoPlay中的逻辑冲突
      baseTime: targetTime,
      currentTime: targetTime,
      pausedTime: targetTime, // 设置为目标时间，让startAutoPlay从这个时间开始
      sliderValue: sliderValue,
      formattedCurrentTime: this.formatTime(targetTime),
      // 更新slider时间范围
      timeRangeStart: this.data.leftBoundaryTime,
      timeRangeEnd: Date.now(),
    });

    // 重新生成刻度
    this.generateTimeScales();

    // 更新录制区域（如果存在）
    if (this.data.isRecording) {
      this._updateRecordAreaNew();
    }

    // 先显示成功提示，避免被后续loading覆盖
    wx.showToast({
      title: "已跳转23小时前",
      icon: "success",
      duration: 2000,
    });

    // 延迟调用回放接口，确保toast显示
    setTimeout(() => {
      this._updateVideoUrlAfterDrag(targetTime);
    }, 500);

    // 确保状态更新完成后再启动自动播放
    wx.nextTick(() => {
      setTimeout(() => {
        // 检查是否还在当前页面且没有被其他操作中断
        if (this.data.currentTime === targetTime) {
          this.setData({ isPlaying: true });
          this.startAutoPlay();
        }
      }, 800); // 延长等待时间，确保视频更新完成
    });
  },

  /**
   * 格式化时间
   */
  formatTime: function (time) {
    const date = new Date(time);
    const hours = date.getHours().toString().padStart(2, "0");
    const minutes = date.getMinutes().toString().padStart(2, "0");
    const seconds = date.getSeconds().toString().padStart(2, "0");
    return `${hours}:${minutes}:${seconds}`;
  },

  /**
   * 更新录制区域
   */
  _updateRecordArea: function () {
    const recordStartTime = this.data.recordStartTime;
    const recordEndTime = this.data.recordEndTime;
    const leftBoundaryTime =
      this.data.leftBoundaryTime || Date.now() - 23 * 60 * 60 * 1000;
    const rightBoundaryTime = this.data.rightBoundaryTime || Date.now();

    const recordStartSliderValue = this._timeToSliderValue(recordStartTime);
    const recordEndSliderValue = this._timeToSliderValue(recordEndTime);

    this.setData({
      recordStartSliderValue: recordStartSliderValue,
      recordEndSliderValue: recordEndSliderValue,
    });
  },

  /**
   * 开始录制
   */
  startRecording: function () {
    const currentTime = this.data.currentTime;
    this.setData({
      isRecording: true,
      recordStartTime: currentTime,
      recordEndTime: currentTime,
    });
  },

  /**
   * 结束录制
   */
  endRecording: function () {
    this.setData({
      isRecording: false,
    });
  },

  /**
   * 拖动录制区域时更新时间
   */
  onRecordAreaChange: function (e) {
    const recordStartSliderValue = e.detail.value[0];
    const recordEndSliderValue = e.detail.value[1];
    const recordStartTime = this._sliderValueToTime(recordStartSliderValue);
    const recordEndTime = this._sliderValueToTime(recordEndSliderValue);

    this.setData({
      recordStartTime: recordStartTime,
      recordEndTime: recordEndTime,
      recordStartSliderValue: recordStartSliderValue,
      recordEndSliderValue: recordEndSliderValue,
    });
  },

  /**
   * 执行差异更新录像列表（优化动画）
   * @param {Array} newList 新的录像列表
   */
  _performDifferentialUpdate(newList) {
    const currentList = this.data.videoList;

    // 如果当前列表为空或者正在切换摄像头，直接设置新列表
    if (!currentList || currentList.length === 0 || this._isSwitchingCamera) {
      const initialList = newList.map((item, index) => ({
        ...this._transformVideoItem(item, index),
        isNew: false, // 切换摄像头或初始加载不显示新增动画
      }));

      this.setData({ videoList: initialList });
      return;
    }

    // 检查是否真的有变化
    if (this._isListIdentical(currentList, newList)) {
      return;
    }

    // 分析变化
    const updates = this._analyzeListChanges(currentList, newList);
    const { toAdd, toUpdate, toRemove, unchanged } = updates;

    // 构建最终列表
    const finalList = this._buildFinalList(updates, newList);

    // 使用 wx.nextTick 确保更新在下一个事件循环中执行
    wx.nextTick(() => {
      this.setData({ videoList: finalList }, () => {
        // 只有真正有状态变化时才移除更新标志
        if (toUpdate.some((item) => item.statusChangeType)) {
          this._removeUpdateFlags();
        }

        // 对于有图片更新的项目，强制重新渲染
        const imageUpdates = toUpdate.filter((item) => item._forceImageUpdate);
        if (imageUpdates.length > 0) {
          this._forceImageRerender(imageUpdates);
        }
      });
    });
  },
  // 停止自动播放
  _performZoom: function (targetLevel) {
    const scaleConfigs = [
      { unit: "second", text: "秒", interval: 1000 }, // 0级：1秒间隔
      { unit: "minute", text: "分钟", interval: 60000 }, // 1级：1分钟间隔
      { unit: "10minute", text: "10分钟", interval: 600000 }, // 2级：10分钟间隔
    ];

    if (targetLevel >= 0 && targetLevel < scaleConfigs.length) {
      const newConfig = scaleConfigs[targetLevel];

      // 设置刻度切换标志，用于节流处理
      this._isScaleSwitching = true;

      // 清除之前可能存在的刻度生成延迟任务
      if (this._scaleGenerationTimeout) {
        clearTimeout(this._scaleGenerationTimeout);
        this._scaleGenerationTimeout = null;
      }

      // 获取新刻度单位的配置，包含动态像素宽度
      const scaleConfig = this._getScaleConfig(newConfig.unit);
      const basePixelWidth = scaleConfig.basePixelWidth;

      // 计算新的pixelsPerSecond，使用动态的基础像素宽度
      const pixelsPerSecond = (basePixelWidth * 1000) / newConfig.interval;

      // 在切换刻度时，确保当前时间对齐到新的刻度间隔
      const currentTime = this.data.currentTime;
      const alignedCurrentTime = this._alignToScaleInterval(
        currentTime,
        newConfig.interval,
      );

      // 如果有录制区域，检查是否需要重置录制状态或调整最短时间限制
      let alignedRecordStartTime = this.data.recordStartTime;
      let alignedRecordEndTime = this.data.recordEndTime;
      let shouldResetRecording = false;

      if (
        this.data.isRecording &&
        this.data.recordStartTime &&
        this.data.recordEndTime
      ) {
        // 计算当前录制区域的时长
        const currentRecordDuration =
          this.data.recordEndTime - this.data.recordStartTime;

        // 根据刻度单位设置最短时间限制
        const minRequiredDuration = this._getMinRecordDuration(newConfig.unit);

        if (currentRecordDuration < minRequiredDuration) {
          // 录制区域太小，需要重置录制状态
          shouldResetRecording = true;
        } else {
          // 录制区域足够大，进行对齐
          alignedRecordStartTime = this._alignToScaleInterval(
            this.data.recordStartTime,
            newConfig.interval,
          );
          alignedRecordEndTime = this._alignToScaleInterval(
            this.data.recordEndTime,
            newConfig.interval,
          );

          // 确保录制区域至少满足最短时间限制
          if (
            alignedRecordEndTime - alignedRecordStartTime <
            minRequiredDuration
          ) {
            alignedRecordEndTime = alignedRecordStartTime + minRequiredDuration;
          }
        }
      }

      // 准备更新的数据
      const updateData = {
        scaleLevel: targetLevel,
        scaleUnit: newConfig.unit,
        scaleUnitText: newConfig.text,
        pixelsPerSecond: pixelsPerSecond,
        currentTime: alignedCurrentTime,
        baseTime: alignedCurrentTime,
        pausedTime: alignedCurrentTime,
        formattedCurrentTime: this.formatTime(alignedCurrentTime),
      };

      // 根据是否需要重置录制状态来更新数据
      if (shouldResetRecording) {
        // 重置录制状态
        updateData.isRecording = false;
        updateData.recordStartTime = 0;
        updateData.recordEndTime = 0;
        updateData.recordStartTimeFormatted = "";
        updateData.recordEndTimeFormatted = "";
        updateData.recordDurationText = "";
        updateData.recordAreaStyle = "display: none;";
      } else {
        // 保持录制状态，更新录制区域时间
        updateData.recordStartTime = alignedRecordStartTime;
        updateData.recordEndTime = alignedRecordEndTime;
        updateData.recordStartTimeFormatted = alignedRecordStartTime
          ? this.formatTime(alignedRecordStartTime)
          : "";
        updateData.recordEndTimeFormatted = alignedRecordEndTime
          ? this.formatTime(alignedRecordEndTime)
          : "";
      }

      // 更新数据
      this.setData(updateData);

      // 重新生成时间刻度
      this.generateTimeScales();

      // 处理录制区域显示
      if (shouldResetRecording) {
        // 显示友好提示
        wx.showToast({
          title: "录制区域过短，请重新绘制",
          icon: "none",
          duration: 4000,
        });
      } else if (this.data.isRecording) {
        // 更新录制区域显示
        this._updateRecordArea();
        this._updateRecordDurationText();
      }

      // 如果在录制中，重新绘制录制区域
      if (this.data.isRecording) {
        this._updateRecordArea();
      }

      // 更新缩放按钮状态
      this._updateZoomButtonStatus();

      // 延迟清除刻度切换标志，确保节流生效
      setTimeout(() => {
        this._isScaleSwitching = false;
      }, 300);
    }
  },
  // 切换播放状态 - 修复暂停恢复时的跳动问题

  // 放大时间轴（显示更精细的时间）
  /**
   * 处理缩略图URL - 简化版本
   * @param {string} coverKey 封面图片key
   * @param {boolean} forceRefresh 是否强制刷新（跳过缓存）
   * @returns {string} 处理后的缩略图URL
   */
  _processThumbnailUrl(coverKey, forceRefresh = false) {
    // 没有coverKey时直接返回兜底图
    if (!coverKey) {
      return "/images/live/slice.png";
    }

    // 如果已经是完整URL
    if (coverKey.startsWith("http")) {
      if (forceRefresh) {
        // 强制刷新时添加时间戳参数
        const separator = coverKey.includes("?") ? "&" : "?";
        return `${coverKey}${separator}t=${Date.now()}`;
      } else {
        // 正常情况下直接返回原URL
        return coverKey;
      }
    }

    // 其他情况返回兜底图
    return "/images/live/slice.png";
  },

  // 缩小时间轴（显示更宏观的时间）
  /**
   * 处理录像列表原始数据
   * @param {Array} rawList 原始数据列表
   * @returns {Array} 处理后的列表
   */
  _processVideoListData(rawList) {
    return rawList
      .map((item, index) => this._transformVideoItem(item, index))
      .filter((item) => item.sliceStatue !== undefined);
  },
  // 更新刻度配置的通用方法
  _rebaseTrack: function (newTime, immediate = false) {
    if (immediate) {
      // 立即调整
      const sliderValue = this._timeToSliderValue(newTime);
      this.setData({
        baseTime: newTime,
        sliderValue: sliderValue,
        dragStartX: this.data.lastTouchX || 0,
        dragStartTime: newTime,
      });
      this.generateTimeScales();
    } else {
      // 延迟调整
      if (this._rebaseTimeout) {
        clearTimeout(this._rebaseTimeout);
      }
      this._rebaseTimeout = setTimeout(() => {
        if (!this.data.isDragging && !this.inertiaAnimationId) {
          const sliderValue = this._timeToSliderValue(newTime);
          this.setData({
            baseTime: newTime,
            sliderValue: sliderValue,
            dragStartX: this.data.lastTouchX,
            dragStartTime: newTime,
          });
          this.generateTimeScales();
        }
        this._rebaseTimeout = null;
      }, 100);
    }
  },
  /**
   * 教程完成后重新绘制轨道和基准线
   * 确保容器宽度已获取，跳转到最新时间并恢复自动播放
   */
  _redrawAfterTutorial: async function () {
    // 确保容器宽度已获取
    if (!this.data.containerWidth) {
      await this._getContainerWidth();
      await this._redrawAfterTutorial();
      return;
    }

    // 跳转到最新时间（与_completeTutorial保持一致）
    const currentRealTime = Date.now();
    const sliderValue = this._timeToSliderValue(currentRealTime);

    this.setData({
      baseTime: currentRealTime,
      currentTime: currentRealTime,
      sliderValue: sliderValue,
      formattedCurrentTime: this.formatTime(currentRealTime),
      // 更新slider时间范围到最新
      timeRangeEnd: currentRealTime,
    });

    // 重新生成刻度
    this.generateTimeScales();

    // 恢复自动播放
    this.startAutoPlay();
  },
  // 检查录制区域在指定baseTime下是否可见 - 用于避免闪烁
  /**
   * 移除更新标志
   */
  _removeUpdateFlags() {
    setTimeout(() => {
      const updatedList = this.data.videoList.map((item) => ({
        ...item,
        isNew: false,
        isUpdated: false,
        isUpdating: false,
        statusChangeType: undefined,
        previousStatus: undefined,
      }));

      this.setData({ videoList: updatedList });
    }, 1500);
  },
  // 确认录制
  _resetClippingState: function () {
    // 停止自动播放和边缘滚动
    this.stopAutoPlay();
    this._stopEdgeScrolling();

    // 重置到实时时间
    const now = this._alignToSecond(Date.now());

    // 重置所有状态
    this.setData({
      isClipping: false,
      isRecording: false,
      isPlaying: true,
      isDragging: false,
      isRecordAreaDragging: false,

      // 重置时间相关状态
      baseTime: now,
      currentTime: now,
      pausedTime: 0,
      trackOffset: 0,
      formattedCurrentTime: this.formatTime(now),

      // 重置剪辑边界设置
      clippingStartTime: null,
      leftBoundaryTime: null,

      // 重置录制区域相关状态
      recordStartTime: 0,
      recordEndTime: 0,
      recordStartTimeFormatted: "",
      recordEndTimeFormatted: "",
      recordAreaStyle: "display: none;",
      recordAreaAnimation: "",
      recordBackupData: null,
      recordDurationText: "",

      // 重置拖拽状态
      recordDragType: "",
      recordDragStartX: 0,
      recordDragOriginalStartTime: 0,
      recordDragOriginalEndTime: 0,
      dragStartX: 0,
      dragStartTime: 0,

      // 重置边缘滚动状态
      isEdgeScrolling: false,
      edgeScrollSpeed: 0,
      edgeScrollTimer: null,
      edgeScrollHandleX: 0,

      // 重置刻度到默认状态
      scaleLevel: 0,
      scaleUnit: "second",
      scaleUnitText: "秒",
      pixelsPerSecond: 10,

      // 重置撤回功能相关状态
      operationHistory: [],
      canRollback: false,

      // 重置播放控制状态
      shouldJumpToStartOnPlay: false,
      _shouldAutoPauseAtRecordEnd: false,
    });

    // 重新生成时间刻度
    this.generateTimeScales();

    // 重新开始自动播放
    this.startAutoPlay();
  },
  // 开始录制或重置录制区域
  _resetToRealTime: function () {
    const now = this._alignToSecond(Date.now());

    this.setData({
      isPlaying: true,
      isDragging: false,
      pausedTime: 0,
      baseTime: now,
      currentTime: now,
      formattedCurrentTime: this.formatTime(now),
      trackOffset: 0,
    });

    this.generateTimeScales();
    this._updateRecordArea();

    // 重新开始自动播放
    if (!this.timer) {
      this.startAutoPlay();
    }
  },
  // 创建新的录制区域 - 重构版本：根据基准线时间和当前时间关系决定录制区域
  _resetToRealTimeAndPlay: function () {
    const now = this._alignToSecond(Date.now());

    // 停止当前播放
    this.stopAutoPlay();

    this.setData({
      isPlaying: true,
      isDragging: false,
      pausedTime: 0,
      baseTime: now,
      currentTime: now,
      formattedCurrentTime: this.formatTime(now),
      trackOffset: 0,
      // 清除录制状态
      isRecording: false,
      recordStartTime: 0,
      recordEndTime: 0,
      recordStartTimeFormatted: "",
      recordEndTimeFormatted: "",
      recordDurationText: "",
      recordAreaStyle: "display: none;",
      // 清除循环播放标志
      shouldJumpToStartOnPlay: false,
      _shouldAutoPauseAtRecordEnd: false,
    });

    // 重新生成刻度
    this.generateTimeScales();

    // 开始自动播放
    this.startAutoPlay();

    // 显示提示
    wx.showToast({
      title: "已回到实时播放",
      icon: "success",
      duration: 1500,
    });
  },

  // 录制区域拖拽结束 - 重构版本：处理边缘滚动和正常拖拽结束
  /**
   * 设置定时器
   * @param {string} name 定时器名称
   * @param {number} timer 定时器ID
   */
  _setTimer(name, timer) {
    this.setData({
      [`timers.${name}`]: timer,
    });
  },
  // 触摸开始 - 支持双指缩放，手柄拖拽由独立事件处理
  /**
   * 显示教程并动态计算高亮框位置
   */
  _showTutorialWithDynamicPosition: function () {
    this.setData(
      {
        showTutorial: true,
        tutorialStep: 1,
        isPlaying: false,
      },
      () => {
        // 在数据更新完成后，动态计算高亮框位置
        wx.nextTick(() => {
          this._updateTutorialHighlightPosition();
        });
      },
    );
  },
  // 重置所有剪辑状态的通用方法
  /**
   * 对录像列表进行排序
   * @param {Array} videoList 录像列表
   * @returns {Array} 排序后的列表
   */
  _sortVideoList(videoList) {
    // 根据startTime倒序排列（最新的在前面）
    // 如果startTime相同或不存在，则使用id作为备用排序字段
    return videoList.sort((a, b) => {
      const aTime = a.startTime || 0;
      const bTime = b.startTime || 0;

      // 优先使用 startTime 排序（倒序，大的在前）
      if (aTime !== bTime) {
        return bTime - aTime;
      }

      // 如果 startTime 相同，使用 id 排序（假设 id 越大越新）
      return (b.id || 0) - (a.id || 0);
    });
  },

  /**
   * 根据设备ID获取设备名称
   * @param {string} devId 设备ID
   * @returns {string} 设备名称
   */
  _getCameraNameByDevId(devId) {
    const { cameraList } = this.data;
    if (!cameraList || !Array.isArray(cameraList)) {
      return devId; // 如果没有摄像头列表，返回原始devId
    }

    const camera = cameraList.find((item) => item.id === devId);
    return camera ? camera.deviceName : devId; // 如果找不到对应的摄像头，返回原始devId
  },
  // 将基准线移动到指定时间位置
  _startEdgeScrolling: function (speed) {
    // 停止之前的滚动
    this._stopEdgeScrolling();

    this.setData({
      isEdgeScrolling: true,
      edgeScrollSpeed: speed,
    });

    // 启动滚动定时器 - 提高频率到144fps，极致跟手性
    this.data.edgeScrollTimer = setInterval(() => {
      if (!this.data.isRecordAreaDragging) {
        this._stopEdgeScrolling();
        return;
      }

      // 改为移动基准时间而不是轨道偏移
      const { pixelsPerSecond } = this.data;
      const timeOffset = (speed / pixelsPerSecond) * 1000; // 转换为毫秒
      const newBaseTime = this.data.baseTime - timeOffset; // 反向移动时间
      const sliderValue = this._timeToSliderValue(newBaseTime);

      // 使用批量更新，减少setData调用次数
      const updateData = {
        baseTime: newBaseTime,
        currentTime: newBaseTime,
        sliderValue: sliderValue,
      };

      // 关键修复：在边缘滚动时，实时更新录制区域时间和显示
      // 使用实时的手柄位置来计算新的录制时间，确保手柄跟随手指
      if (this.data.isRecording && this.data.edgeScrollHandleX) {
        // 直接计算新的录制时间，避免额外的方法调用
        const { recordDragType, containerWidth, baseTime, pixelsPerSecond } =
          this.data;
        let currentBaseTime = baseTime;
        if (!this.data.isPlaying) {
          currentBaseTime = this._alignToSecond(baseTime);
        }

        const centerPosition = containerWidth / 2;
        const relativePosition = this.data.edgeScrollHandleX - centerPosition;
        let handlerTime =
          newBaseTime + (relativePosition / pixelsPerSecond) * 1000;

        // 边界检查
        const realCurrentTime = Date.now();
        const leftBoundaryTime = this.data.leftBoundaryTime;

        if (leftBoundaryTime && handlerTime < leftBoundaryTime) {
          handlerTime = leftBoundaryTime;
        }
        if (handlerTime > realCurrentTime) {
          handlerTime = realCurrentTime;
        }

        // 批量更新录制时间
        let newStartTime = this.data.recordStartTime;
        let newEndTime = this.data.recordEndTime;

        if (recordDragType === "start") {
          newStartTime = handlerTime;
          updateData.recordStartTime = newStartTime;
          updateData.recordStartTimeFormatted = this.formatTime(newStartTime);
        } else if (recordDragType === "end") {
          newEndTime = handlerTime;
          updateData.recordEndTime = newEndTime;
          updateData.recordEndTimeFormatted = this.formatTime(newEndTime);
        }

        // 更新录制时长
        const duration = (newEndTime - newStartTime) / 1000;
        // 使用智能时长格式化函数
        updateData.recordDurationText = this.formatDuration(duration);

        // 直接计算录制区域样式，实现边缘滚动时的极致跟手性
        const recordAreaStyle = this._calculateRecordAreaStyleDirect(
          newStartTime,
          newEndTime,
        );
        if (recordAreaStyle) {
          updateData.recordAreaStyle = recordAreaStyle;
        }
      }

      // 一次性更新所有数据
      this.setData(updateData);

      // 重新生成刻度以保持同步
      if (!this._isJumping) {
        this.generateTimeScales();
      }
    }, 4); // 250fps 更新频率，极致跟手性
  },
  // 直接跳转到指定时间
  _startInertiaAnimationOptimized: function (initialVelocity) {
    // 停止之前的惯性动画
    this._stopInertiaAnimation();

    // 预计算动画关键帧
    const keyframes = this._generateInertiaKeyframes(initialVelocity);

    if (keyframes.length === 0) {
      // 速度太小，直接吸附
      const snappedTime = this._calculateNearestScaleTime(
        this.data.currentTime,
      );
      this._finalizeTrackPositionSync(snappedTime);
      return;
    }

    let currentFrame = 0;
    const startTime = Date.now();

    const animate = () => {
      const elapsed = Date.now() - startTime;

      // 找到当前应该播放的关键帧
      while (
        currentFrame < keyframes.length - 1 &&
        elapsed >= keyframes[currentFrame + 1].time
      ) {
        currentFrame++;
      }

      if (currentFrame >= keyframes.length - 1) {
        // 动画结束
        this._stopInertiaAnimation();
        const finalKeyframe = keyframes[keyframes.length - 1];
        this._updateTimeAndUI(
          finalKeyframe.currentTime,
          finalKeyframe.displacement,
        );

        // 检查是否达到时间限制
        if (finalKeyframe.reachedTimeLimit) {
          // 直接同步到当前时间位置
          this._finalizeTrackPositionSync(finalKeyframe.currentTime);

          // 设置播放状态并启动播放
          this.setData({ isPlaying: true });
          setTimeout(() => {
            if (!this.data.isDragging && !this.inertiaAnimationId) {
              this.startAutoPlay();
            }
          }, 50); // 缩短延迟，让播放更快开始
        } else if (finalKeyframe.reachedLeftBoundary) {
          // 直接同步到左边界时间位置
          this._finalizeTrackPositionSync(finalKeyframe.currentTime);

          wx.showToast({
            title: "已到达最早可查看时间",
            icon: "none",
            duration: 1500,
          });
        } else {
          // 正常结束，吸附到最近刻度
          const snappedTime = this._calculateNearestScaleTime(
            finalKeyframe.currentTime,
          );
          this._finalizeTrackPositionSync(snappedTime);

          // 惯性动画结束后调用回放接口更新视频链接
          this._updateVideoUrlAfterDrag(snappedTime);
        }
        return;
      }

      // 在两个关键帧之间插值
      const currentKeyframe = keyframes[currentFrame];
      const nextKeyframe = keyframes[currentFrame + 1];

      if (nextKeyframe) {
        const frameProgress =
          (elapsed - currentKeyframe.time) /
          (nextKeyframe.time - currentKeyframe.time);
        const smoothProgress = this._easeOutCubic(frameProgress);

        const interpolatedTime =
          currentKeyframe.currentTime +
          (nextKeyframe.currentTime - currentKeyframe.currentTime) *
            smoothProgress;
        const interpolatedDisplacement =
          currentKeyframe.displacement +
          (nextKeyframe.displacement - currentKeyframe.displacement) *
            smoothProgress;

        this._updateTimeAndUI(interpolatedTime, interpolatedDisplacement);
      }

      // 使用更高的帧率 - 优化为更流畅的动画
      this.inertiaAnimationId = setTimeout(animate, 8); // 8ms = 125fps，平衡性能和流畅度
    };

    animate();
  },
  // 检查目标元素是否在录制区域内
  _startScaleColorTimer: function () {
    // 清除现有定时器
    this._stopScaleColorTimer();

    // 创建新的定时器，每秒更新一次刻度颜色
    this.scaleColorTimer = setInterval(() => {
      this._updateScaleColors();
    }, 1000); // 每秒更新一次
  },
  // 单指拖拽开始 - 简化版本
  _stopEdgeScrolling: function () {
    if (this.data.edgeScrollTimer) {
      clearInterval(this.data.edgeScrollTimer);
    }

    this.setData({
      isEdgeScrolling: false,
      edgeScrollSpeed: 0,
      edgeScrollTimer: null,
    });
  },
  // 双指缩放开始
  _stopInertiaAnimation: function () {
    if (this.inertiaAnimationId) {
      clearTimeout(this.inertiaAnimationId);
      this.inertiaAnimationId = null;
    }
    this.lastInertiaTime = null;
  },
  // 触摸移动 - 支持双指缩放和单指拖拽，手柄拖拽由独立事件处理
  _stopScaleColorTimer: function () {
    if (this.scaleColorTimer) {
      clearInterval(this.scaleColorTimer);
      this.scaleColorTimer = null;
    }
  },
  // 单指拖拽移动 - 简化版本
  /**
   * 转换录像项数据
   * @param {Object} item 原始录像项
   * @param {number} index 在列表中的索引
   * @returns {Object} 转换后的录像项
   */
  _transformVideoItem(item, index = 0) {
    const startTimeDisplay = this._formatSecondsToTime(item.startTime);
    const endTimeDisplay = this._formatSecondsToTime(item.endTime);
    const duration = this._calculateDuration(item.startTime, item.endTime);

    // 根据索引生成录像名称，格式为 录像001, 录像002...
    const videoNumber = (index + 1).toString().padStart(3, "0");
    const videoName = item.name || `录像${videoNumber}`;

    // 获取摄像头名称
    const cameraName = this._getCameraNameByDevId(item.devId);

    const thumbnailUrl = this._processThumbnailUrl(item.coverKey);

    return {
      ...item,
      name: videoName,
      startTimeDisplay,
      endTimeDisplay,
      duration,
      cameraName, // 添加摄像头名称字段
      thumbnail: thumbnailUrl,
    };
  },
  // 双指缩放移动
  _updateBaselinePosition: function () {
    const { containerWidth, pixelRatio, deviceType } = this.data;

    if (!containerWidth) {
      return;
    }

    // 计算基准线的精确位置（容器中心）
    const centerPosition = containerWidth / 2;
    let alignedCenterPosition;

    // 根据设备类型进行精确对齐
    if (
      (deviceType === "large-phone" || deviceType === "tablet") &&
      pixelRatio
    ) {
      // 大屏设备：对齐到物理像素
      alignedCenterPosition =
        Math.round(centerPosition * pixelRatio) / pixelRatio;
    } else {
      // 小屏设备：对齐到逻辑像素
      alignedCenterPosition = Math.round(centerPosition);
    }

    // 设置基准线样式
    const baselineStyle = `left: ${alignedCenterPosition}px; transform: translateX(-50%);`;

    this.setData({
      baselineStyle: baselineStyle,
    });
  },
  // 实时更新时间和界面
  /**
   * 更新摄像头列表（基于索引的逻辑）
   * @param {Array} newList 新的摄像头列表
   */
  _updateCameraList(newList) {
    const currentActiveIndex = this.data.activateCameraIndex;
    const currentList = this.data.cameraList;

    // 检查当前激活的摄像头是否还存在
    let newActiveIndex = currentActiveIndex;

    if (
      currentActiveIndex >= 0 &&
      currentActiveIndex < currentList.length &&
      currentActiveIndex < newList.length
    ) {
      const currentActiveCamera = currentList[currentActiveIndex];
      const newCameraAtSameIndex = newList[currentActiveIndex];

      // 如果同一索引位置的摄像头ID不同，尝试在新列表中找到原来的摄像头
      if (currentActiveCamera.id !== newCameraAtSameIndex.id) {
        const foundIndex = newList.findIndex(
          (camera) => camera.id === currentActiveCamera.id,
        );
        if (foundIndex !== -1) {
          newActiveIndex = foundIndex;
        } else {
          // 如果找不到原来的摄像头，保持当前索引（如果有效）
          newActiveIndex =
            currentActiveIndex < newList.length ? currentActiveIndex : 0;
        }
      }
    } else {
      // 如果当前索引无效，重置为0
      newActiveIndex = newList.length > 0 ? 0 : -1;
    }

    // 更新摄像头列表和激活索引
    const updateData = {
      cameraList: newList,
    };

    // 如果激活索引发生变化，更新相关数据
    if (newActiveIndex !== currentActiveIndex && newActiveIndex >= 0) {
      updateData.activateCameraIndex = newActiveIndex;
      updateData.videoUrl = newList[newActiveIndex].liveUrl;
      updateData.playStartTime = Date.now();
    }

    this.setData(updateData);

    // 如果摄像头发生变化，刷新录像列表
    if (newActiveIndex !== currentActiveIndex) {
      setTimeout(() => {
        this.getVideoList();
      }, 500);
    }
  },

  /**
   * 更新缩放按钮状态
   */
  _updateZoomButtonStatus: function () {
    const { scaleLevel } = this.data;
    const maxLevel = 2; // 最大刻度级别（10分钟）- 修复：删除5分钟后最大级别是2
    const minLevel = 0; // 最小刻度级别（秒）

    this.setData({
      canZoomIn: scaleLevel > minLevel, // 当前级别大于最小级别时可以放大
      canZoomOut: scaleLevel < maxLevel, // 当前级别小于最大级别时可以缩小
    });
  },
  // 基于DOM刻度重构录制区域绘制 - 完全基于刻度DOM位置
  _updateRecordAreaByDOMScales: function () {
    const {
      recordStartTime,
      recordEndTime,
      isRecording,
      timeScales,
      containerWidth,
    } = this.data;

    // 如果不在录制状态或没有录制时间，隐藏录制区域
    if (
      !isRecording ||
      !recordStartTime ||
      !recordEndTime ||
      !timeScales.length
    ) {
      this.setData({ recordAreaStyle: "display: none;" });
      return;
    }

    // 找到最接近录制开始和结束时间的刻度DOM位置
    const startScale = this._findClosestScaleForDOM(recordStartTime);
    const endScale = this._findClosestScaleForDOM(recordEndTime);

    if (!startScale || !endScale) {
      console.warn("无法找到录制区域对应的刻度DOM:", {
        recordStartTime: new Date(recordStartTime).toLocaleString(),
        recordEndTime: new Date(recordEndTime).toLocaleString(),
        timeScalesCount: timeScales.length,
        firstScale: timeScales[0]
          ? new Date(timeScales[0].time).toLocaleString()
          : "none",
        lastScale: timeScales[timeScales.length - 1]
          ? new Date(timeScales[timeScales.length - 1].time).toLocaleString()
          : "none",
      });
      this.setData({ recordAreaStyle: "display: none;" });
      return;
    }

    // 使用刻度的实际DOM位置来绘制录制区域
    const originalLeft = Math.min(startScale.position, endScale.position);
    const originalRight = Math.max(startScale.position, endScale.position);
    const originalWidth = Math.abs(originalRight - originalLeft);

    // 计算可见部分的边界
    const visibleLeft = Math.max(originalLeft, 0);
    const visibleRight = Math.min(originalRight, containerWidth);
    const visibleWidth = Math.max(visibleRight - visibleLeft, 0);

    // 使用与_calculateRecordAreaStyleDirect相同的严格条件
    // 新策略：录制区域始终跟随时间刻度显示，不进行边界隐藏
    // 让录制区域和手柄完全按照时间刻度的位置来显示
    // 如果时间刻度在页面外，录制区域和手柄也应该在页面外

    const displayLeft = originalLeft;
    const displayWidth = Math.max(originalWidth, 60);

    // 标记为可见状态（不再进行边界隐藏）
    this._recordAreaHidden = false;

    // 使用绿色渐变背景，确保样式完整
    const gradientBackground =
      "linear-gradient(90deg, rgba(76, 175, 80, 0.3) 0%, rgba(139, 195, 74, 0.25) 50%, rgba(76, 175, 80, 0.3) 100%)";

    this.setData({
      recordAreaStyle: `
        display: block;
        left: ${displayLeft}px;
        width: ${displayWidth}px;
        position: absolute;
        height: 100rpx;
        background: ${gradientBackground};
        border: 2rpx solid rgba(76, 175, 80, 0.6);
        box-shadow: 0 0 8rpx rgba(76, 175, 80, 0.3);
        min-width: 60rpx;
        box-sizing: border-box;
        transition: none !important;
      `
        .replace(/\s+/g, " ")
        .trim(),
    });
  },

  // 直接更新录制区域 - 无动画，提高拖拽跟手性，完全基于DOM刻度
  _updateRecordAreaDirectly: function () {
    const {
      recordStartTime,
      recordEndTime,
      isRecording,
      timeScales,
      containerWidth,
    } = this.data;

    // 如果不在录制状态或没有录制时间，隐藏录制区域
    if (
      !isRecording ||
      !recordStartTime ||
      !recordEndTime ||
      !timeScales.length
    ) {
      this.setData({ recordAreaStyle: "display: none;" });
      return;
    }

    // 使用DOM基础方法找到最接近录制开始和结束时间的刻度
    const startScale = this._findClosestScaleForDOM(recordStartTime);
    const endScale = this._findClosestScaleForDOM(recordEndTime);

    if (!startScale || !endScale) {
      this.setData({ recordAreaStyle: "display: none;" });
      return;
    }

    // 使用刻度的实际DOM位置来绘制录制区域
    const left = Math.min(startScale.position, endScale.position);
    const right = Math.max(startScale.position, endScale.position);
    const width = Math.abs(right - left);

    // 确保录制区域有最小宽度
    const finalWidth = Math.max(width, 20);

    // 简化的可见性检查
    const isVisible = !(left + finalWidth < -20 || left > containerWidth + 20);

    if (!isVisible) {
      this.setData({ recordAreaStyle: "display: none;" });
      return;
    }

    // 使用绿色渐变背景，直接设置样式，无过渡动画
    const gradientBackground =
      "linear-gradient(90deg, rgba(76, 175, 80, 0.3) 0%, rgba(139, 195, 74, 0.25) 50%, rgba(76, 175, 80, 0.3) 100%)";

    this.setData({
      recordAreaStyle: `
        display: block;
        left: ${left}px;
        width: ${finalWidth}px;
        position: absolute;
        height: 100rpx;
        background: ${gradientBackground};
        border: 2rpx solid rgba(76, 175, 80, 0.6);
        box-shadow: 0 0 8rpx rgba(76, 175, 80, 0.3);
        min-width: 60rpx;
        box-sizing: border-box;
        transition: none !important;
      `
        .replace(/\s+/g, " ")
        .trim(),
    });
  },
  // 触摸结束 - 支持双指缩放和单指拖拽，手柄拖拽由独立事件处理
  _updateRecordAreaRealtime: function () {
    // 确保清除任何动画类名
    if (this.data.recordAreaAnimation) {
      this.setData({
        recordAreaAnimation: "",
      });
    }

    // 在拖拽或惯性动画过程中，减少录制区域更新频率以避免闪动
    if (this.data.isDragging || this.inertiaAnimationId) {
      // 使用防抖机制，减少更新频率
      if (this._recordAreaUpdateTimeout) {
        clearTimeout(this._recordAreaUpdateTimeout);
      }
      this._recordAreaUpdateTimeout = setTimeout(() => {
        this._updateRecordAreaByDOMScales();
        this._recordAreaUpdateTimeout = null;
      }, 16); // 限制为60fps
    } else {
      // 非拖拽状态下正常更新
      this._updateRecordAreaByDOMScales();
    }
  },
  // 单指拖拽结束 - 添加惯性滑动
  _updateRecordDurationText: function () {
    const { recordStartTime, recordEndTime } = this.data;
    if (recordStartTime && recordEndTime) {
      const durationSeconds = (recordEndTime - recordStartTime) / 1000;
      // 使用智能时长格式化函数，而不是简单的秒数显示
      const durationText = this.formatDuration(durationSeconds);
      this.setData({
        recordDurationText: durationText,
      });
    }
  },
  // 计算拖拽速度
  _updateRecordTimeByHandlePosition: function (handleX) {
    const { recordDragType, containerWidth, currentTime, pixelsPerSecond } =
      this.data;

    // 使用与刻度生成相同的逻辑：相对于currentTime计算
    const centerTime = currentTime || this.data.baseTime;
    const centerPosition = containerWidth / 2;

    // 计算手柄当前位置对应的时间
    const relativePosition = handleX - centerPosition;
    let handlerTime = centerTime + (relativePosition / pixelsPerSecond) * 1000;

    // 移除右侧手柄的当前时间限制，允许用户选择过去的任意时间段
    // 在录制回放场景中，用户应该能够自由选择历史时间范围

    // 获取当前录制区域的时间
    let newStartTime = this.data.recordStartTime;
    let newEndTime = this.data.recordEndTime;

    // 计算最小6个刻度的时间间隔
    const scaleInterval = this._getScaleConfig(this.data.scaleUnit).interval;
    const minDuration = 6 * scaleInterval; // 最小6个刻度的时间长度

    if (recordDragType === "start") {
      // 拖拽开始手柄，应用最小范围限制
      let proposedStartTime = handlerTime;

      // 检查是否会导致录制区域小于最小范围
      if (newEndTime - proposedStartTime < minDuration) {
        proposedStartTime = newEndTime - minDuration;
      }

      newStartTime = proposedStartTime;
    } else if (recordDragType === "end") {
      // 拖拽结束手柄，应用最小范围限制
      let proposedEndTime = handlerTime;

      // 检查是否会导致录制区域小于最小范围
      if (proposedEndTime - newStartTime < minDuration) {
        proposedEndTime = newStartTime + minDuration;
      }

      newEndTime = proposedEndTime;
    }

    // 更新录制时间
    this.setData({
      recordStartTime: newStartTime,
      recordEndTime: newEndTime,
      recordStartTimeFormatted: this.formatTime(newStartTime),
      recordEndTimeFormatted: this.formatTime(newEndTime),
    });

    // 更新录制时长文本和显示（实时更新，无动画）
    this._updateRecordDurationText();
    this._updateRecordAreaRealtime();
  },

  // 极致跟手的录制区域实时更新方法 - 重构为严格六个刻度限制
  _updateRecordTimeByHandlePositionUltraOptimized: function (handleX) {
    const {
      recordDragType,
      containerWidth,
      currentTime,
      pixelsPerSecond,
      recordStartTime,
      recordEndTime,
      recordDragOriginalStartTime,
      recordDragOriginalEndTime,
      leftBoundaryTime,
    } = this.data;

    // 使用与刻度生成相同的逻辑：相对于currentTime计算
    const centerTime = currentTime || this.data.baseTime;
    const centerPosition = containerWidth / 2;

    // 计算手柄当前位置对应的时间
    const relativePosition = handleX - centerPosition;
    let handlerTime = centerTime + (relativePosition / pixelsPerSecond) * 1000;

    // 边界检查
    const realCurrentTime = Date.now();
    if (leftBoundaryTime && handlerTime < leftBoundaryTime) {
      handlerTime = leftBoundaryTime;
    }
    if (handlerTime > realCurrentTime) {
      handlerTime = realCurrentTime;
    }

    // 计算最小6个刻度的时间间隔
    const scaleInterval = this._getScaleConfig(this.data.scaleUnit).interval;
    const minDuration = 6 * scaleInterval; // 最小6个刻度的时间长度

    // 准备批量更新数据
    const updateData = {};

    // 获取当前实际的录制区域时间，用于判断当前区域大小
    const currentStartTime = recordStartTime;
    const currentEndTime = recordEndTime;
    const currentDuration = currentEndTime - currentStartTime;

    // 根据拖拽类型更新对应的时间，并应用严格的六个刻度限制
    if (recordDragType === "start") {
      // 拖拽开始手柄
      let proposedStartTime = handlerTime;

      // 确保不超过边界
      if (leftBoundaryTime && proposedStartTime < leftBoundaryTime) {
        proposedStartTime = leftBoundaryTime;
      }

      // 严格的六个刻度限制逻辑
      if (currentDuration <= minDuration) {
        // 当前已经是最小范围（六个刻度），只允许向外拖拽（扩大区域）
        if (proposedStartTime > currentStartTime) {
          // 向右拖拽（缩小区域），保持在当前位置，不允许缩小
          proposedStartTime = currentStartTime;
        }
        // 向左拖拽（扩大区域）允许，使用proposedStartTime
      } else {
        // 当前大于最小范围，检查是否会小于六个刻度
        if (currentEndTime - proposedStartTime < minDuration) {
          // 会小于六个刻度，固定在六个刻度的边界位置
          proposedStartTime = currentEndTime - minDuration;
        }
        // 否则允许自由拖拽
      }

      updateData.recordStartTime = proposedStartTime;
      updateData.recordStartTimeFormatted = this.formatTime(proposedStartTime);
    } else if (recordDragType === "end") {
      // 拖拽结束手柄
      let proposedEndTime = handlerTime;

      // 确保不超过当前时间
      if (proposedEndTime > realCurrentTime) {
        proposedEndTime = realCurrentTime;
      }

      // 严格的六个刻度限制逻辑
      if (currentDuration <= minDuration) {
        // 当前已经是最小范围（六个刻度），只允许向外拖拽（扩大区域）
        if (proposedEndTime < currentEndTime) {
          // 向左拖拽（缩小区域），保持在当前位置，不允许缩小
          proposedEndTime = currentEndTime;
        }
        // 向右拖拽（扩大区域）允许，使用proposedEndTime
      } else {
        // 当前大于最小范围，检查是否会小于六个刻度
        if (proposedEndTime - currentStartTime < minDuration) {
          // 会小于六个刻度，固定在六个刻度的边界位置
          proposedEndTime = currentStartTime + minDuration;
        }
        // 否则允许自由拖拽
      }

      updateData.recordEndTime = proposedEndTime;
      updateData.recordEndTimeFormatted = this.formatTime(proposedEndTime);
    }

    // 计算并更新录制时长（使用更新后的时间）
    const finalStartTime = updateData.recordStartTime || currentStartTime;
    const finalEndTime = updateData.recordEndTime || currentEndTime;
    const duration = (finalEndTime - finalStartTime) / 1000;
    // 使用智能时长格式化函数
    updateData.recordDurationText = this.formatDuration(duration);

    // 直接计算录制区域样式，避免DOM查询延迟
    const recordAreaStyle = this._calculateRecordAreaStyleDirect(
      finalStartTime,
      finalEndTime,
    );
    if (recordAreaStyle) {
      updateData.recordAreaStyle = recordAreaStyle;
    }

    // 一次性批量更新，实现零延迟跟手
    this.setData(updateData);
  },

  /**
   * 直接计算录制区域样式 - 简化版本，适配slider结构
   * @param {number} startTime 录制开始时间
   * @param {number} endTime 录制结束时间
   * @returns {string} 录制区域样式字符串
   */
  _calculateRecordAreaStyleDirect: function (startTime, endTime) {
    const { containerWidth, currentTime, pixelsPerSecond } = this.data;

    if (!startTime || !endTime || startTime >= endTime) {
      return "display: none;";
    }

    const centerPosition = containerWidth / 2;

    // 使用与刻度生成相同的逻辑：相对于currentTime计算位置
    const centerTime = currentTime || this.data.baseTime;
    const startRelativePosition =
      ((startTime - centerTime) / 1000) * pixelsPerSecond;
    const endRelativePosition =
      ((endTime - centerTime) / 1000) * pixelsPerSecond;

    const startPosition = centerPosition + startRelativePosition;
    const endPosition = centerPosition + endRelativePosition;

    const leftPosition = Math.min(startPosition, endPosition);
    const rightPosition = Math.max(startPosition, endPosition);
    const width = Math.max(rightPosition - leftPosition, 60); // 最小宽度60rpx

    // 新策略：录制区域始终跟随时间刻度显示，不进行边界隐藏
    // 移除边界检查，让录制区域完全按照时间刻度位置显示

    // 构建录制区域样式 - 固定高度，避免高度变化
    return `
      display: block;
      left: ${leftPosition}px;
      width: ${width}px;
      position: absolute;
      height: 100rpx;
      background: linear-gradient(90deg, rgba(76, 175, 80, 0.3) 0%, rgba(139, 195, 74, 0.25) 50%, rgba(76, 175, 80, 0.3) 100%);
      border: 2rpx solid rgba(76, 175, 80, 0.6);
      box-shadow: 0 0 8rpx rgba(76, 175, 80, 0.3);
      min-width: 60rpx;
      box-sizing: border-box;
      transition: none !important;
    `
      .replace(/\s+/g, " ")
      .trim();
  },

  // 优化的录制时间更新方法 - 无动画，直接更新，提高跟手性
  _updateRecordTimeByHandlePositionOptimized: function (handleX) {
    const { recordDragType, containerWidth, currentTime, pixelsPerSecond } =
      this.data;

    // 使用与刻度生成相同的逻辑：相对于currentTime计算
    const centerTime = currentTime || this.data.baseTime;
    const centerPosition = containerWidth / 2;

    // 计算手柄当前位置对应的时间
    const relativePosition = handleX - centerPosition;
    let handlerTime = centerTime + (relativePosition / pixelsPerSecond) * 1000;

    // 严格的边界检查 - 确保录制区域不超过边界
    const realCurrentTime = Date.now();
    const { leftBoundaryTime } = this.data;

    // 获取当前录制区域的时间
    let newStartTime = this.data.recordStartTime;
    let newEndTime = this.data.recordEndTime;

    // 计算最小6个刻度的时间间隔
    const scaleInterval = this._getScaleConfig(this.data.scaleUnit).interval;
    const minDuration = 6 * scaleInterval; // 最小6个刻度的时间长度

    // 根据拖拽类型更新对应的时间，并应用最小范围限制
    if (recordDragType === "start") {
      // 拖拽开始手柄
      let proposedStartTime = handlerTime;

      // 左边界限制：不能早于leftBoundaryTime
      if (leftBoundaryTime && proposedStartTime < leftBoundaryTime) {
        proposedStartTime = leftBoundaryTime;
      }

      // 检查是否会导致录制区域小于最小范围
      if (newEndTime - proposedStartTime < minDuration) {
        proposedStartTime = newEndTime - minDuration;
      }

      // 确保不小于0
      if (proposedStartTime < 0) {
        proposedStartTime = 0;
      }

      newStartTime = proposedStartTime;
    } else if (recordDragType === "end") {
      // 拖拽结束手柄
      let proposedEndTime = handlerTime;

      // 右边界限制：不能晚于当前真实时间
      if (proposedEndTime > realCurrentTime) {
        proposedEndTime = realCurrentTime;
      }

      // 检查是否会导致录制区域小于最小范围
      if (proposedEndTime - newStartTime < minDuration) {
        proposedEndTime = newStartTime + minDuration;

        // 如果调整后仍然超过当前时间，则调整开始时间
        if (proposedEndTime > realCurrentTime) {
          proposedEndTime = realCurrentTime;
          newStartTime = proposedEndTime - minDuration;
        }
      }

      newEndTime = proposedEndTime;
    }

    // 直接更新录制时间，无动画
    this.setData({
      recordStartTime: newStartTime,
      recordEndTime: newEndTime,
      recordStartTimeFormatted: this.formatTime(newStartTime),
      recordEndTimeFormatted: this.formatTime(newEndTime),
    });

    // 更新录制时长文本和显示（直接更新，无过渡）
    this._updateRecordDurationText();
    this._updateRecordAreaDirectly();
  },
  // 方案3：使用优化的插值算法和批量更新
  _updateRecordTimeForEdgeScrolling: function (
    fixedHandleX,
    currentTrackOffset,
  ) {
    const { recordDragType, containerWidth, baseTime, pixelsPerSecond } =
      this.data;

    // 获取当前使用的baseTime（与录制区域计算保持一致）
    let currentBaseTime = baseTime;
    if (!this.data.isPlaying) {
      currentBaseTime = this._alignToSecond(baseTime);
    }

    const centerPosition = containerWidth / 2;

    // 使用当前的trackOffset计算手柄位置对应的时间
    // 这样确保随着轨道滚动，录制时间会相应更新
    const relativePosition = fixedHandleX - centerPosition - currentTrackOffset;
    let handlerTime =
      currentBaseTime + (relativePosition / pixelsPerSecond) * 1000;

    // 移除右侧手柄的当前时间限制，允许用户选择过去的任意时间段

    // 获取当前录制区域的时间
    let newStartTime = this.data.recordStartTime;
    let newEndTime = this.data.recordEndTime;

    // 根据拖拽类型更新对应的时间，并应用最短时间限制
    const minRecordDuration = this._getMinRecordDuration(this.data.scaleUnit);

    if (recordDragType === "start") {
      newStartTime = handlerTime;
      // 确保录制区域满足最短时间限制
      if (newEndTime - newStartTime < minRecordDuration) {
        newStartTime = newEndTime - minRecordDuration;
      }
      if (newStartTime < 0) {
        newStartTime = 0;
        // 如果开始时间被限制为0，需要调整结束时间以满足最短时间限制
        if (newEndTime - newStartTime < minRecordDuration) {
          newEndTime = newStartTime + minRecordDuration;
        }
      }
    } else if (recordDragType === "end") {
      newEndTime = handlerTime;
      // 确保录制区域满足最短时间限制
      if (newEndTime - newStartTime < minRecordDuration) {
        newEndTime = newStartTime + minRecordDuration;
      }
    }

    // 更新录制时间
    this.setData({
      recordStartTime: newStartTime,
      recordEndTime: newEndTime,
      recordStartTimeFormatted: this.formatTime(newStartTime),
      recordEndTimeFormatted: this.formatTime(newEndTime),
    });

    // 更新录制时长文本和显示
    this._updateRecordDurationText();
    this._updateRecordAreaByDOMScales();
  },

  // 优化的边缘滚动录制时间更新方法 - 提高性能和跟手性
  _updateRecordTimeForEdgeScrollingOptimized: function (
    fixedHandleX,
    currentTrackOffset,
  ) {
    const { recordDragType, containerWidth, baseTime, pixelsPerSecond } =
      this.data;

    // 获取当前使用的baseTime（与录制区域计算保持一致）
    let currentBaseTime = baseTime;
    if (!this.data.isPlaying) {
      currentBaseTime = this._alignToSecond(baseTime);
    }

    const centerPosition = containerWidth / 2;

    // 使用当前的trackOffset计算手柄位置对应的时间
    const relativePosition = fixedHandleX - centerPosition - currentTrackOffset;
    let handlerTime =
      currentBaseTime + (relativePosition / pixelsPerSecond) * 1000;

    // 严格的边界检查 - 确保录制区域不超过边界
    const realCurrentTime = Date.now();
    const { leftBoundaryTime } = this.data;

    // 获取当前录制区域的时间
    let newStartTime = this.data.recordStartTime;
    let newEndTime = this.data.recordEndTime;

    // 根据拖拽类型更新对应的时间
    if (recordDragType === "start") {
      newStartTime = handlerTime;

      // 左边界限制：不能早于leftBoundaryTime
      if (leftBoundaryTime && newStartTime < leftBoundaryTime) {
        newStartTime = leftBoundaryTime;
      }

      // 确保开始时间不超过结束时间
      const scaleConfig = this._getScaleConfig(this.data.scaleUnit);
      const minInterval = scaleConfig.interval;
      if (newStartTime >= newEndTime) {
        newStartTime = newEndTime - minInterval;
      }
      if (newStartTime < 0) {
        newStartTime = 0;
      }
    } else if (recordDragType === "end") {
      newEndTime = handlerTime;

      // 右边界限制：不能晚于当前真实时间
      if (newEndTime > realCurrentTime) {
        newEndTime = realCurrentTime;
      }

      // 确保结束时间不小于开始时间
      const scaleConfig = this._getScaleConfig(this.data.scaleUnit);
      const minInterval = scaleConfig.interval;
      if (newEndTime <= newStartTime) {
        newEndTime = newStartTime + minInterval;
        // 如果调整后仍然超过当前时间，则调整开始时间
        if (newEndTime > realCurrentTime) {
          newEndTime = realCurrentTime;
          newStartTime = newEndTime - minInterval;
        }
      }
    }

    // 直接更新录制时间，无动画
    this.setData({
      recordStartTime: newStartTime,
      recordEndTime: newEndTime,
      recordStartTimeFormatted: this.formatTime(newStartTime),
      recordEndTimeFormatted: this.formatTime(newEndTime),
    });

    // 更新录制时长文本和显示（直接更新，无过渡）
    this._updateRecordDurationText();
    this._updateRecordAreaDirectly();
  },

  // 智能跟手方案 - 预测性更新 + 跳帧优化
  _updateRecordTimeByHandlePositionUltimate: function (handleX) {
    const now = Date.now();

    // 初始化速度追踪
    if (!this._dragTracker) {
      this._dragTracker = {
        positions: [],
        lastUpdateTime: now,
        skipFrameCount: 0,
        lastHandleX: handleX,
      };
    }

    // 记录位置和时间用于速度计算
    this._dragTracker.positions.push({ x: handleX, time: now });
    if (this._dragTracker.positions.length > 3) {
      this._dragTracker.positions.shift(); // 只保留最近3个位置
    }

    // 计算拖拽速度（像素/毫秒）
    let velocity = 0;
    if (this._dragTracker.positions.length >= 2) {
      const latest =
        this._dragTracker.positions[this._dragTracker.positions.length - 1];
      const previous = this._dragTracker.positions[0];
      const timeDiff = latest.time - previous.time;
      if (timeDiff > 0) {
        velocity = Math.abs(latest.x - previous.x) / timeDiff;
      }
    }

    // 根据速度决定更新策略
    const isHighSpeed = velocity > 2; // 超过2像素/毫秒认为是高速拖拽
    const timeSinceLastUpdate = now - this._dragTracker.lastUpdateTime;

    if (isHighSpeed) {
      // 高速拖拽：跳帧更新，只在关键帧更新
      this._dragTracker.skipFrameCount++;

      // 每3帧更新一次，或者距离上次更新超过33ms（约30fps）
      if (this._dragTracker.skipFrameCount >= 3 || timeSinceLastUpdate >= 33) {
        this._performFastUpdate(handleX);
        this._dragTracker.skipFrameCount = 0;
        this._dragTracker.lastUpdateTime = now;
      } else {
        // 跳帧期间：只更新视觉位置，不计算时间
        this._performVisualOnlyUpdate(handleX);
      }
    } else {
      // 低速拖拽：正常更新频率，但优化计算
      if (timeSinceLastUpdate >= 16) {
        // 60fps限制
        this._performNormalUpdate(handleX);
        this._dragTracker.lastUpdateTime = now;
      }
    }

    this._dragTracker.lastHandleX = handleX;
  },

  // 快速更新：简化计算，只更新关键数据
  _performFastUpdate: function (handleX) {
    const {
      recordDragType,
      containerWidth,
      baseTime,
      pixelsPerSecond,
      trackOffset,
    } = this.data;

    // 使用缓存的基础数据，避免重复计算
    if (
      !this._cachedBaseData ||
      Date.now() - this._cachedBaseData.timestamp > 100
    ) {
      this._cachedBaseData = {
        currentBaseTime: this.data.isPlaying
          ? baseTime
          : this._alignToSecond(baseTime),
        centerPosition: containerWidth / 2,
        realCurrentTime: Date.now(),
        leftBoundaryTime: this.data.leftBoundaryTime,
        timestamp: Date.now(),
      };
    }

    // 快速计算时间
    const relativePosition =
      handleX - this._cachedBaseData.centerPosition - trackOffset;
    let handlerTime =
      this._cachedBaseData.currentBaseTime +
      (relativePosition / pixelsPerSecond) * 1000;

    // 简化边界检查
    if (
      this._cachedBaseData.leftBoundaryTime &&
      handlerTime < this._cachedBaseData.leftBoundaryTime
    ) {
      handlerTime = this._cachedBaseData.leftBoundaryTime;
    }
    if (handlerTime > this._cachedBaseData.realCurrentTime) {
      handlerTime = this._cachedBaseData.realCurrentTime;
    }

    // 只更新关键数据
    let updateData = {};
    if (recordDragType === "start") {
      updateData.recordStartTime = handlerTime;
      this.data.recordStartTime = handlerTime; // 同步更新内存数据
    } else if (recordDragType === "end") {
      updateData.recordEndTime = handlerTime;
      this.data.recordEndTime = handlerTime; // 同步更新内存数据
    }

    // 快速计算录制区域样式
    const newStartTime =
      recordDragType === "start" ? handlerTime : this.data.recordStartTime;
    const newEndTime =
      recordDragType === "end" ? handlerTime : this.data.recordEndTime;

    updateData.recordAreaStyle = this._calculateRecordAreaStyleDirect(
      newStartTime,
      newEndTime,
    );

    this.setData(updateData);
  },

  // 视觉更新：只更新录制区域样式，不计算时间
  _performVisualOnlyUpdate: function (handleX) {
    // 使用预测算法估算位置
    const { recordDragType, containerWidth, pixelsPerSecond, trackOffset } =
      this.data;
    let estimatedStartTime = this.data.recordStartTime;
    let estimatedEndTime = this.data.recordEndTime;

    // 基于当前手柄位置快速估算时间
    if (this._cachedBaseData) {
      const relativePosition = handleX - containerWidth / 2 - trackOffset;
      const estimatedTime =
        this._cachedBaseData.currentBaseTime +
        (relativePosition / pixelsPerSecond) * 1000;

      if (recordDragType === "start") {
        estimatedStartTime = estimatedTime;
      } else if (recordDragType === "end") {
        estimatedEndTime = estimatedTime;
      }
    }

    // 只更新视觉样式
    const recordAreaStyle = this._calculateRecordAreaStyleDirect(
      estimatedStartTime,
      estimatedEndTime,
    );
    this.setData({ recordAreaStyle });
  },

  // 正常更新：完整计算但优化性能
  _performNormalUpdate: function (handleX) {
    // 清除过期缓存，确保数据准确性
    if (
      this._cachedBaseData &&
      Date.now() - this._cachedBaseData.timestamp > 200
    ) {
      this._cachedBaseData = null;
    }

    // 执行完整更新
    this._performFastUpdate(handleX);

    // 延迟更新非关键数据
    if (!this._formatUpdatePending) {
      this._formatUpdatePending = true;
      wx.nextTick(() => {
        const newStartTime = this.data.recordStartTime;
        const newEndTime = this.data.recordEndTime;
        this.setData({
          recordStartTimeFormatted: this.formatTime(newStartTime),
          recordEndTimeFormatted: this.formatTime(newEndTime),
          recordDurationText: this.formatDuration(
            (newEndTime - newStartTime) / 1000,
          ),
        });
        this._formatUpdatePending = false;
      });
    }
  },

  /**
   * Promise化的缩放操作
   * 确保每次缩放完全完成后才允许下一次操作，使用async/await处理异步刻度生成
   * @param {number} newLevel 新的缩放级别
   * @param {Object} newConfig 新的刻度配置
   * @param {number} pixelsPerSecond 每秒像素数
   * @param {string} action 操作类型
   * @returns {Promise<void>} 缩放完成的Promise
   */
  _updateScaleAsync: async function (
    newLevel,
    newConfig,
    pixelsPerSecond,
    action,
  ) {
    try {
      // 设置缩放操作标志
      this._isScaleSwitching = true;

      // 执行同步的缩放逻辑
      this._updateScaleSync(newLevel, newConfig, pixelsPerSecond, action);

      // 异步生成刻度，传递正确的刻度单位
      await this.generateTimeScalesAsync(newConfig.unit);

      // 更新缩放按钮状态
      this._updateZoomButtonStatus();
    } catch (error) {
      console.error("刻度生成失败:", error);
      throw error;
    } finally {
      // 重置刻度切换标志
      this._isScaleSwitching = false;
    }
  },

  // 生成惯性动画的关键帧
  _updateScale: function (newLevel, newConfig, pixelsPerSecond, action) {
    // 如果有录制区域，检查是否需要重置录制状态
    let shouldResetRecording = false;
    let alignedRecordStartTime = this.data.recordStartTime;
    let alignedRecordEndTime = this.data.recordEndTime;

    if (
      this.data.isRecording &&
      this.data.recordStartTime &&
      this.data.recordEndTime
    ) {
      // 计算当前录制区域的时长
      const currentRecordDuration =
        this.data.recordEndTime - this.data.recordStartTime;

      // 检查录制区域是否太小（小于2个新刻度间隔）
      const minRequiredDuration = 2 * newConfig.interval;

      if (currentRecordDuration < minRequiredDuration) {
        // 录制区域太小，需要重置录制状态
        shouldResetRecording = true;
      } else {
        // 录制区域足够大，进行对齐
        alignedRecordStartTime = this._alignToScaleInterval(
          this.data.recordStartTime,
          newConfig.interval,
        );
        alignedRecordEndTime = this._alignToScaleInterval(
          this.data.recordEndTime,
          newConfig.interval,
        );

        // 确保录制区域至少有一个刻度间隔的长度
        if (alignedRecordEndTime <= alignedRecordStartTime) {
          alignedRecordEndTime = alignedRecordStartTime + newConfig.interval;
        }
      }
    }

    // 对齐当前时间到新的刻度间隔，确保基准线和刻度对齐
    // 特别处理：如果在自动播放状态，确保不会超过当前真实时间
    const currentTime = this.data.currentTime;
    const realCurrentTime = Date.now();
    const isAutoPlaying = this.data.isPlaying && !this.data.isDragging;

    let alignedCurrentTime;
    let alignedBaseTime;

    if (isAutoPlaying && currentTime > realCurrentTime) {
      // 如果在自动播放且当前时间超过真实时间，使用真实时间对齐
      alignedCurrentTime = this._alignToScaleInterval(
        realCurrentTime,
        newConfig.interval,
      );
      alignedBaseTime = this._alignToScaleInterval(
        realCurrentTime,
        newConfig.interval,
      );
    } else {
      // 否则使用当前时间对齐
      alignedCurrentTime = this._alignToScaleInterval(
        currentTime,
        newConfig.interval,
      );
      alignedBaseTime = this._alignToScaleInterval(
        this.data.baseTime,
        newConfig.interval,
      );
    }

    // 准备更新的数据
    const updateData = {
      scaleLevel: newLevel,
      scaleUnit: newConfig.unit,
      scaleUnitText: newConfig.text,
      pixelsPerSecond: pixelsPerSecond,
      currentTime: alignedCurrentTime,
      baseTime: alignedBaseTime,
      pausedTime: alignedCurrentTime,
      formattedCurrentTime: this.formatTime(alignedCurrentTime),
      trackOffset: 0, // 重置轨道偏移，确保对齐
    };

    // 根据是否需要重置录制状态来更新数据
    if (shouldResetRecording) {
      // 重置录制状态
      updateData.isRecording = false;
      updateData.recordStartTime = 0;
      updateData.recordEndTime = 0;
      updateData.recordStartTimeFormatted = "";
      updateData.recordEndTimeFormatted = "";
      updateData.recordDurationText = "";
      updateData.recordAreaStyle = "display: none;";
    } else if (this.data.isRecording) {
      // 保持录制状态，更新录制区域时间
      updateData.recordStartTime = alignedRecordStartTime;
      updateData.recordEndTime = alignedRecordEndTime;
      updateData.recordStartTimeFormatted = alignedRecordStartTime
        ? this.formatTime(alignedRecordStartTime)
        : "";
      updateData.recordEndTimeFormatted = alignedRecordEndTime
        ? this.formatTime(alignedRecordEndTime)
        : "";
    }

    // 更新数据
    this.setData(updateData, () => {
      // 在数据更新完成后立即重新生成时间刻度，强制重新生成
      this.generateTimeScales(true);

      // 处理录制区域显示
      if (shouldResetRecording) {
        // 显示友好提示
        wx.showToast({
          title: "录制区域过短，请重新绘制",
          icon: "none",
          duration: 2000,
        });
      } else if (this.data.isRecording) {
        // 如果在录制中，检查录制区域是否在可视范围内再决定是否重新绘制
        if (this._isRecordAreaPotentiallyVisible()) {
          // 立即更新，不使用延迟和过渡动画，确保手柄拖拽跟手
          this._updateRecordArea(true); // 使用实时更新
          this._updateRecordDurationText();
        } else {
          // 录制区域不在可视范围内，直接隐藏，避免闪烁
          this.setData({ recordAreaStyle: "display: none;" });
        }
      }

      // 重置刻度切换标志
      this._isScaleSwitching = false;
    });

    // 更新缩放按钮状态
    this._updateZoomButtonStatus();
  },

  /**
   * 同步的缩放逻辑，从_updateScale中分离出来
   */
  _updateScaleSync: function (
    newLevel,
    newConfig,
    pixelsPerSecond,
    action,
    shouldResetRecording,
    alignedRecordStartTime,
    alignedRecordEndTime,
  ) {
    // 如果参数未传入，重新计算录制区域相关参数
    if (shouldResetRecording === undefined) {
      shouldResetRecording = false;
      alignedRecordStartTime = this.data.recordStartTime;
      alignedRecordEndTime = this.data.recordEndTime;

      if (
        this.data.isRecording &&
        this.data.recordStartTime &&
        this.data.recordEndTime
      ) {
        // 计算当前录制区域的时长
        const currentRecordDuration =
          this.data.recordEndTime - this.data.recordStartTime;

        // 检查录制区域是否太小（小于2个新刻度间隔）
        const minRequiredDuration = 2 * newConfig.interval;

        if (currentRecordDuration < minRequiredDuration) {
          // 录制区域太小，需要重置录制状态
          shouldResetRecording = true;
        } else {
          // 录制区域足够大，进行对齐
          alignedRecordStartTime = this._alignToScaleInterval(
            this.data.recordStartTime,
            newConfig.interval,
          );
          alignedRecordEndTime = this._alignToScaleInterval(
            this.data.recordEndTime,
            newConfig.interval,
          );

          // 确保录制区域至少有一个刻度间隔的长度
          if (alignedRecordEndTime <= alignedRecordStartTime) {
            alignedRecordEndTime = alignedRecordStartTime + newConfig.interval;
          }
        }
      }
    }

    // 对齐当前时间到新的刻度间隔，确保基准线和刻度对齐
    // 特别处理：如果在自动播放状态，确保不会超过当前真实时间
    const currentTime = this.data.currentTime;
    const realCurrentTime = Date.now();
    const isAutoPlaying = this.data.isPlaying && !this.data.isDragging;

    let alignedCurrentTime;
    let alignedBaseTime;

    if (isAutoPlaying && currentTime > realCurrentTime) {
      // 如果在自动播放且当前时间超过真实时间，使用真实时间对齐
      alignedCurrentTime = this._alignToScaleInterval(
        realCurrentTime,
        newConfig.interval,
      );
      alignedBaseTime = this._alignToScaleInterval(
        realCurrentTime,
        newConfig.interval,
      );
    } else {
      // 否则使用当前时间对齐
      alignedCurrentTime = this._alignToScaleInterval(
        currentTime,
        newConfig.interval,
      );
      alignedBaseTime = this._alignToScaleInterval(
        this.data.baseTime,
        newConfig.interval,
      );
    }

    // 准备更新的数据
    const updateData = {
      scaleLevel: newLevel,
      scaleUnit: newConfig.unit,
      scaleUnitText: newConfig.text,
      pixelsPerSecond: pixelsPerSecond,
      currentTime: alignedCurrentTime,
      baseTime: alignedBaseTime,
      pausedTime: alignedCurrentTime,
      formattedCurrentTime: this.formatTime(alignedCurrentTime),
      trackOffset: 0, // 重置轨道偏移，确保对齐
    };

    // 根据是否需要重置录制状态来更新数据
    if (shouldResetRecording) {
      // 重置录制状态
      updateData.isRecording = false;
      updateData.recordStartTime = 0;
      updateData.recordEndTime = 0;
      updateData.recordStartTimeFormatted = "";
      updateData.recordEndTimeFormatted = "";
      updateData.recordDurationText = "";
      updateData.recordAreaStyle = "display: none;";
    } else if (this.data.isRecording) {
      // 保持录制状态，更新录制区域时间
      updateData.recordStartTime = alignedRecordStartTime;
      updateData.recordEndTime = alignedRecordEndTime;
      updateData.recordStartTimeFormatted = alignedRecordStartTime
        ? this.formatTime(alignedRecordStartTime)
        : "";
      updateData.recordEndTimeFormatted = alignedRecordEndTime
        ? this.formatTime(alignedRecordEndTime)
        : "";
    }

    // 更新数据
    this.setData(updateData);

    // 处理录制区域显示（同步版本，不显示Toast）
    if (shouldResetRecording) {
      // 录制区域过短时的处理已在异步版本中处理
    } else if (this.data.isRecording) {
      // 如果在录制中，检查录制区域是否在可视范围内再决定是否重新绘制
      if (this._isRecordAreaPotentiallyVisible()) {
        // 立即更新，不使用延迟和过渡动画，确保手柄拖拽跟手
        this._updateRecordArea(true); // 使用实时更新
        this._updateRecordDurationText();
      } else {
        // 录制区域不在可视范围内，直接隐藏，避免闪烁
        this.setData({ recordAreaStyle: "display: none;" });
      }
    }
  },

  // 三次贝塞尔缓动函数
  _updateScaleColors: function () {
    const timeScales = this.data.timeScales;
    if (!timeScales || timeScales.length === 0) {
      return;
    }

    const realCurrentTime = Date.now();
    let hasChanges = false;

    // 检查每个刻度的未来状态是否需要更新，避免修改只读数组
    const updatedTimeScales = [];

    try {
      for (let i = 0; i < timeScales.length; i++) {
        const scale = timeScales[i];
        const newIsFuture = scale.time > realCurrentTime;

        if (scale.isFuture !== newIsFuture) {
          hasChanges = true;
          // 创建新对象，避免修改只读对象
          updatedTimeScales.push({
            time: scale.time,
            position: scale.position,
            scaleType: scale.scaleType,
            timeText: scale.timeText || "",
            isFuture: newIsFuture,
          });
        } else {
          // 创建副本，避免引用只读对象
          updatedTimeScales.push({
            time: scale.time,
            position: scale.position,
            scaleType: scale.scaleType,
            timeText: scale.timeText || "",
            isFuture: scale.isFuture || false,
          });
        }
      }
    } catch (error) {
      console.error("处理刻度颜色更新失败:", error);
      return; // 出错时直接返回，不更新
    }

    // 只有在状态发生变化时才更新数据
    if (hasChanges) {
      this.setData({
        timeScales: updatedTimeScales,
      });
    }
  },
  // 停止惯性滑动动画
  _updateTimeAndUI: function (newTime, offset) {
    // 防止无效时间
    if (!newTime || isNaN(newTime)) {
      return;
    }

    // 检查左边界限制，防止拖拽超出边界
    const { leftBoundaryTime } = this.data;
    let finalTime = newTime;
    let finalOffset = offset;

    if (leftBoundaryTime && newTime < leftBoundaryTime) {
      // 如果超出左边界，限制在边界位置
      finalTime = leftBoundaryTime;
      // 重新计算对应的offset
      const timeDiff = this.data.dragStartTime - finalTime;
      finalOffset = (timeDiff / 1000) * this.data.pixelsPerSecond;
    }

    // 在拖拽过程中，更新时间和slider值
    const sliderValue = this._timeToSliderValue(finalTime);
    this.setData({
      currentTime: finalTime,
      formattedCurrentTime: this.formatTime(finalTime),
      pausedTime: finalTime,
      sliderValue: sliderValue,
    });

    // 实时更新录制区域（如果在录制中）- 优化为同步更新，减少闪动
    if (this.data.isRecording && !this.data.isRecordAreaDragging) {
      // 只有在不拖拽录制区域手柄时才更新录制区域，避免冲突和闪动
      this._updateRecordAreaRealtime(finalTime, finalOffset);
    }

    // 检查是否需要动态生成更多刻度
    // 如果不是在跳转过程中，才进行刻度检查
    if (!this._isJumping) {
      this._checkAndUpdateScales(finalOffset);
    }

    // 当偏移过大时，重新调整基准以保持流畅性，但只在拖拽结束后进行
    const maxOffset = this.data.containerWidth * 1.2; // 增加阈值，减少重新调整频率
    if (
      Math.abs(finalOffset) > maxOffset &&
      this.data.containerWidth > 0 &&
      !this.data.isDragging &&
      !this.inertiaAnimationId
    ) {
      this._rebaseTrack(finalTime, true); // 使用立即模式
    }
  },


  // 轨道快速更新：简化计算，只更新核心数据
  _performTrackFastUpdate: function (newTime, offset) {
    // 检查左边界限制
    const { leftBoundaryTime } = this.data;
    let finalTime = newTime;
    let finalOffset = offset;

    if (leftBoundaryTime && newTime < leftBoundaryTime) {
      finalTime = leftBoundaryTime;
      const timeDiff = this.data.dragStartTime - finalTime;
      finalOffset = (timeDiff / 1000) * this.data.pixelsPerSecond;
    }

    // 使用缓存的格式化时间，避免重复计算
    if (
      !this._trackFormatCache ||
      Date.now() - this._trackFormatCache.timestamp > 100
    ) {
      this._trackFormatCache = {
        formattedTime: this.formatTime(finalTime),
        timestamp: Date.now(),
      };
    }

    // 只更新关键数据
    this.setData({
      currentTime: finalTime,
      pausedTime: finalTime,
      trackOffset: finalOffset,
      formattedCurrentTime: this._trackFormatCache.formattedTime,
    });

    // 延迟更新录制区域，避免阻塞主线程
    if (this.data.isRecording && !this.data.isRecordAreaDragging) {
      if (!this._recordAreaUpdatePending) {
        this._recordAreaUpdatePending = true;
        wx.nextTick(() => {
          this._updateRecordAreaRealtime(finalTime, finalOffset);
          this._recordAreaUpdatePending = false;
        });
      }
    }
  },

  // 轨道视觉更新：只更新轨道位置，跳过复杂计算
  _performTrackVisualUpdate: function (newTime, offset) {
    // 检查边界
    const { leftBoundaryTime } = this.data;
    let finalTime = newTime;
    let finalOffset = offset;

    if (leftBoundaryTime && newTime < leftBoundaryTime) {
      finalTime = leftBoundaryTime;
      const timeDiff = this.data.dragStartTime - finalTime;
      finalOffset = (timeDiff / 1000) * this.data.pixelsPerSecond;
    }

    // 只更新轨道偏移，保持视觉流畅
    this.setData({
      trackOffset: finalOffset,
    });

    // 同步更新内存中的时间数据
    this.data.currentTime = finalTime;
    this.data.pausedTime = finalTime;
  },

  // 轨道正常更新：完整更新但优化性能
  _performTrackNormalUpdate: function (newTime, offset) {
    // 清除过期缓存
    if (
      this._trackFormatCache &&
      Date.now() - this._trackFormatCache.timestamp > 200
    ) {
      this._trackFormatCache = null;
    }

    // 执行快速更新
    this._performTrackFastUpdate(newTime, offset);

    // 延迟执行复杂操作
    if (!this._trackComplexUpdatePending) {
      this._trackComplexUpdatePending = true;
      setTimeout(() => {
        // 检查刻度更新
        if (!this._isJumping) {
          this._checkAndUpdateScales(offset);
        }

        // 检查是否需要重新调整基准
        const maxOffset = this.data.containerWidth * 1.2;
        if (
          Math.abs(offset) > maxOffset &&
          this.data.containerWidth > 0 &&
          !this.data.isDragging &&
          !this.inertiaAnimationId
        ) {
          this._rebaseTrack(newTime, true);
        }

        this._trackComplexUpdatePending = false;
      }, 50); // 50ms后执行复杂操作
    }
  },
  // 双指缩放结束
  /**
   * 动态更新教程高亮框位置
   */
  _updateTutorialHighlightPosition: function () {
    const currentStep = this.data.tutorialStep;
    const targetClass = this.data.tutorialData[currentStep].target;

    // 根据不同步骤选择不同的选择器
    let selector = "";
    switch (targetClass) {
      case "cut-icon":
        selector = ".cut-icon";
        break;
      case "gesture-operation-box":
        selector = ".gesture-operation-box";
        break;
      case "save-slice-btn":
        selector = ".save-slice-btn";
        break;
      default:
        selector = `.${targetClass}`;
    }

    // 获取目标元素的位置信息
    const query = wx.createSelectorQuery().in(this);
    query
      .select(selector)
      .boundingClientRect((rect) => {
        if (rect) {
          // 动态设置高亮框的位置和大小
          const highlightStyle = {
            position: "absolute",
            left: `${rect.left}px`,
            top: `${rect.top}px`,
            width: `${rect.width}px`,
            height: `${rect.height}px`,
            zIndex: 9999,
          };

          this.setData({
            tutorialHighlightStyle: highlightStyle,
            tutorialHighlightReady: true,
          });
        } else {
          console.warn(`未找到目标元素: ${selector}`);
          // 如果找不到元素，使用默认样式
          this._useFallbackHighlightStyle(targetClass);
        }
      })
      .exec();
  },
  // 计算两点之间的距离
  _updateVideoUrlAfterDrag: async function (dragEndTime) {
    // 防止重复调用
    if (this._isUpdatingVideo) {
      return;
    }

    // 检查是否有可用的摄像头
    if (!this.data.cameraList || this.data.cameraList.length === 0) {
      return;
    }

    // 获取当前激活摄像头的 id
    const currentCamera = this.data.cameraList[this.data.activateCameraIndex];
    if (!currentCamera || !currentCamera.id) {
      return;
    }

    // 将时间转换为时间戳（秒）
    const timestamp = Math.floor(dragEndTime / 1000);

    // 检查是否与当前视频时间戳相同，避免重复请求
    if (this._lastVideoTimestamp === timestamp) {
      return;
    }

    this._isUpdatingVideo = true;
    this._lastVideoTimestamp = timestamp;

    try {
      // 显示加载提示 - 使用更短的延迟，避免闪烁
      const loadingTimer = setTimeout(() => {
        wx.showLoading({
          title: "加载回放...",
          mask: true,
        });
      }, 300); // 300ms后才显示loading，避免快速操作时的闪烁

      // 调用回放接口
      const res = await liveReplay({
        id: currentCamera.id,
        timeStamp: timestamp,
      });

      // 清除loading定时器
      clearTimeout(loadingTimer);
      wx.hideLoading();

      // 检查返回的视频链接是否有效
      if (!res.data) {
        throw new Error("回放接口返回空链接");
      }

      // 重置视频状态标志
      this._videoLoadedData = false;
      this._videoCanPlay = false;
      this._needPauseAfterLoad = true; // 标记需要在加载完成后暂停

      // 更新视频链接并设置为暂停状态
      this.setData({
        videoUrl: res.data,
        isPlaying: false,
      });

    } catch (error) {
      console.error("回放失败:", error);
      wx.hideLoading();

      // 只在非网络错误时显示toast，避免频繁提示
      if (!error.message || !error.message.includes("request:fail")) {
        wx.showToast({
          title: "回放失败，请重试",
          icon: "none",
          duration: 2000,
        });
      }
    } finally {
      this._isUpdatingVideo = false;
    }
  },
  // 执行缩放操作
  /**
   * 使用备用的高亮样式（当动态获取失败时）
   */
  _useFallbackHighlightStyle: function (targetClass) {
    this.setData({
      tutorialHighlightStyle: null, // 使用CSS中定义的样式
      tutorialHighlightReady: true,
    });
  },
  // 计算最近的刻度时间（适用于所有刻度模式）
  /**
   * 切换静音状态
   */
  changeMute() {
    this.setData({
      isMuted: !this.data.isMuted,
    });
  },
  // 拖拽结束后更新视频链接
  /**
   * 检查用户切片权益
   * @returns {Object|null} 权益信息
   */
  async checkUserRights() {
    try {
      const { code, data, msg } = await acquiringRights({ 1: "1" });

      if (code === 0) {
        const remainSeconds = data.remain || 0;
        this.setData({
          userRights: {
            total: data.total || 0,
            remain: remainSeconds,
            remainFormatted: this._formatSecondsToDuration(remainSeconds),
          },
        });

        return {
          total: data.total || 0,
          remain: remainSeconds,
          remainFormatted: this._formatSecondsToDuration(remainSeconds),
        };
      } else {
        console.error("获取权益失败:", msg);
        return null;
      }
    } catch (error) {
      console.error("检查用户权益失败:", error);
      return null;
    }
  },
  // 重置到实时时间 - 简化版本
  closeClip: function () {
    this._resetClippingState();
    //if (!this.data.isRecording) {
    //	this._resetClippingState();
    //	return
    //}
    //wx.showModal({
    //	title: '退出剪辑',
    //	content: '确定要退出剪辑页面吗？当前的剪辑进度将会丢失。',
    //	confirmText: '确定退出',
    //	cancelText: '取消',
    //	confirmColor: '#ff6b6b',
    //	success: (res) => {
    //		if (res.confirm) {
    //			// 用户确认退出，重置所有剪辑状态
    //			this._resetClippingState();
    //		}
    //		// 用户取消，不做任何操作
    //	}
    //});
  },
  // 确认保存切片
  confirmRecord: async function () {
    if (!this.data.isRecording) {
      return;
    }

    // 计算录制时长
    const { recordStartTime, recordEndTime } = this.data;
    let durationText = "未知";
    let durationSeconds = 0;

    if (recordStartTime && recordEndTime) {
      durationSeconds = Math.abs(recordEndTime - recordStartTime) / 1000;
      // 使用智能时长格式化函数
      durationText = this.formatDuration(durationSeconds);
    }

    // 检查权益是否足够
    wx.showLoading({
      title: "检查权益中...",
      mask: true,
    });

    try {
      const rights = await this.checkUserRights();
      wx.hideLoading();

      // 格式化剩余权益时长
      const remainDurationText =
        rights && rights.remain > 0
          ? this.formatDuration(rights.remain)
          : "00:00:00";

      // 检查录制时长是否超过剩余权益
      const isRightsInsufficient =
        !rights || rights.remain <= 0 || durationSeconds > rights.remain;
      //const isRightsInsufficient = true;
      if (isRightsInsufficient) {
        // 权益不足的情况
        wx.showModal({
          title: "保存切片",
          content: `录制时长：${durationText}\n剩余权益：${remainDurationText}\n权益不足请购买资源包获得精彩片段`,
          confirmText: "立即支付",
          cancelText: "返回剪辑",
          confirmColor: "#4CAF50",
          success: async (res) => {
            if (res.confirm) {
              // 用户选择立即支付
              await this.handlePayment();
            }
            // 用户取消，不做任何操作
          },
        });
      } else {
        // 权益足够的情况
        wx.showModal({
          title: "保存切片",
          content: `录制时长：${durationText}\n剩余权益：${remainDurationText}\n将会扣除相应的权益时长`,
          confirmText: "确认保存",
          cancelText: "返回剪辑",
          confirmColor: "#4CAF50",
          success: async (res) => {
            if (res.confirm) {
              await this.handleRecordComplete();
            }
            // 用户取消，不做任何操作
          },
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error("检查权益失败:", error);
      wx.showToast({
        title: "检查权益失败，请重试",
        icon: "none",
      });
    }
  },

  /**
   * 处理支付流程
   */
  handlePayment: async function () {
    // 检查用户登录状态
    if (!app.globalData.userInfo || !app.globalData.userInfo.userId) {
      wx.navigateTo({
        url: "/pages/login/index",
      });
      return;
    }

    try {
      // 创建订单
      wx.showLoading({
        title: "创建订单中...",
        mask: true,
      });

      // 获取当前录制的相关信息作为订单业务ID
      const { recordStartTime, recordEndTime } = this.data;
      const durationSeconds = Math.abs(recordEndTime - recordStartTime) / 1000;

      // 使用录制时长作为业务ID，类型为直播切片(2)
      const orderRes = await sliceVideoCreateOrderApi({
        assetPkgId: 1, // 资产包编号,示例值(1)
        siteCode: this.data.siteDetail.siteCode, // 场馆编号,示例值(1)
      });

      wx.hideLoading();

      if (orderRes.code === 0) {
        // 创建订单成功，开始支付
        await this.payOrder(orderRes.data.payOrderId);
      } else {
        wx.showToast({
          title: orderRes.msg || "创建订单失败",
          icon: "none",
          duration: 2000,
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error("创建订单失败:", error);
      wx.showToast({
        title: "创建订单失败，请重试",
        icon: "none",
        duration: 2000,
      });
    }
  },

  /**
   * 支付订单
   * @param {string} payOrderId 支付订单ID
   */
  payOrder: async function (payOrderId) {
    try {
      wx.showLoading({
        title: "获取支付信息...",
        mask: true,
      });

      const payRes = await payOrderApi({
        id: payOrderId,
        channelCode: "wx_lite",
        channelExtras: { openid: app.globalData.userInfo.openId },
      });

      wx.hideLoading();

      if (payRes.code === 0) {
        this.setData({
          prePayInfo: payRes.data,
        });
        await this.callWxPay();
      } else {
        wx.showToast({
          title: payRes.msg || "获取支付信息失败",
          icon: "none",
          duration: 2000,
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error("获取支付信息失败:", error);
      wx.showToast({
        title: "获取支付信息失败，请重试",
        icon: "none",
        duration: 2000,
      });
    }
  },

  /**
   * 调用微信支付
   */
  callWxPay: async function () {
    try {
      const prePayInfo = this.data.prePayInfo;
      if (!prePayInfo || !prePayInfo.displayContent) {
        wx.showToast({
          title: "支付信息异常",
          icon: "none",
          duration: 2000,
        });
        return;
      }

      // 解析支付参数
      const paymentParams = JSON.parse(prePayInfo.displayContent);

      // 发起微信支付
      const payResult = await new Promise((resolve, reject) => {
        wx.requestPayment({
          timeStamp: paymentParams.timeStamp,
          nonceStr: paymentParams.nonceStr,
          package: paymentParams.packageValue,
          signType: paymentParams.signType,
          paySign: paymentParams.paySign,
          success: (res) => resolve(res),
          fail: (res) => reject(res),
        });
      });

      // 支付成功
      wx.showToast({
        title: "支付成功",
        icon: "success",
        duration: 2000,
      });

      // 支付成功后，继续执行录制完成逻辑
      setTimeout(async () => {
        await this.handleRecordComplete();
      }, 2000);
    } catch (error) {
      console.error("支付失败:", error);
      wx.showToast({
        title: "支付失败",
        icon: "none",
        duration: 2000,
      });
    }
  },

  /**
   * 执行删除录像项（优化版本）
   * @param {string} videoId 录像ID
   */
  async deleteVideoItem(videoId) {

    wx.showLoading({
      title: "删除中...",
      mask: true,
    });

    try {
      // 先从前端列表中移除该项，提供即时反馈
      const currentList = this.data.videoList || [];
      const optimisticList = currentList.filter(item => item.id !== videoId);

      this.setData({ videoList: optimisticList });

      // 调用删除API
      const res = await deleteSlicingTask({ id: videoId });

      if (res.code === 0) {

        // 删除成功，刷新列表确保数据一致性
        await this.getVideoList();

        wx.showToast({
          title: "删除成功",
          icon: "success",
        });
      } else {
        console.error(`删除失败 - ID: ${videoId}, 错误: ${res.msg}`);

        // 删除失败，恢复原列表
        this.setData({ videoList: currentList });

        wx.showToast({
          title: res.msg || "删除失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("删除录像失败:", error);

      // 发生异常，恢复原列表
      const currentList = this.data.videoList || [];
      const originalList = [...currentList];
      // 如果当前列表中没有该项，说明已经被乐观删除，需要恢复
      if (!currentList.find(item => item.id === videoId)) {
        // 重新获取列表
        await this.getVideoList();
      }

      wx.showToast({
        title: "删除失败",
        icon: "none",
      });
    } finally {
      wx.hideLoading();
    }
  },
  // 启动刻度颜色更新定时器
  errorAndReturn: function () {
    wx.showModal({
      title: "提示",
      content: "暂无可实时观看的画面，请稍后再试",
      showCancel: false,
      confirmText: "确定",
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack({
            delta: 1,
          });
        }
      },
    });
  },
  // 停止刻度颜色更新定时器
  formatDuration: function (duration) {
    // 修复逻辑：直接将输入当作秒数处理，不再进行毫秒/秒的判断
    // 因为所有调用此函数的地方都已经将时间转换为秒数
    const durationSeconds = Math.floor(duration);

    const hours = Math.floor(durationSeconds / 3600);
    const minutes = Math.floor((durationSeconds % 3600) / 60);
    const seconds = Math.floor(durationSeconds % 60);

    if (hours > 0) {
      // 超过一小时：显示 x时x分x秒
      return `${hours}时${minutes}分${seconds}秒`;
    } else if (minutes > 0) {
      // 超过一分钟但未超过一小时：显示 x分x秒
      return `${minutes}分${seconds}秒`;
    } else {
      // 未超过一分钟：显示 xx秒
      return `${seconds}秒`;
    }
  },

  /**
   * Promise化的刻度生成方法
   * 确保刻度生成完成后才resolve，防止重复渲染，支持强制刻度单位
   * @param {string} forceScaleUnit 强制使用的刻度单位，用于解决快速切换时的时序问题
   * @returns {Promise<void>} 刻度生成完成的Promise
   */
  generateTimeScalesAsync: async function (forceScaleUnit) {
    // 防止重复渲染：如果正在生成刻度，等待当前生成完成
    if (this._isGeneratingScales) {
      // 如果已经有等待队列，直接返回现有的Promise
      if (this._scaleGenerationPromise) {
        return this._scaleGenerationPromise;
      }

      // 创建等待Promise
      this._scaleGenerationPromise = new Promise((waitResolve, waitReject) => {
        const checkInterval = setInterval(async () => {
          if (!this._isGeneratingScales) {
            clearInterval(checkInterval);
            this._scaleGenerationPromise = null;
            try {
              // 递归调用，确保生成最新的刻度，传递强制刻度单位
              await this.generateTimeScalesAsync(forceScaleUnit);
              waitResolve();
            } catch (error) {
              waitReject(error);
            }
          }
        }, 10); // 每10ms检查一次

        // 设置超时，避免无限等待
        setTimeout(() => {
          clearInterval(checkInterval);
          this._scaleGenerationPromise = null;
          waitReject(new Error("刻度生成超时"));
        }, 5000); // 5秒超时
      });

      return this._scaleGenerationPromise;
    }

    // 缓存优化：如果时间变化很小且没有强制刻度单位，跳过重新生成
    const { currentTime } = this.data;
    if (
      !forceScaleUnit &&
      this._lastScaleGenerateTime &&
      Math.abs(currentTime - this._lastScaleGenerateTime) < 100
    ) {
      return; // 时间变化小于100ms，直接返回
    }

    this._isGeneratingScales = true;
    this._lastScaleGenerateTime = currentTime;

    // 使用nextTick确保在下一个事件循环中执行，避免阻塞UI
    return new Promise((resolve, reject) => {
      wx.nextTick(() => {
        try {
          this._generateTimeScalesSync(forceScaleUnit);
          resolve();
        } catch (error) {
          console.error("异步刻度生成失败:", error);
          reject(error);
        } finally {
          this._isGeneratingScales = false;
        }
      });
    });
  },

  // 回到最新直播画面
  generateTimeScales: function (forceRegenerate = false) {
    // 防止重复渲染：如果正在生成刻度，直接返回
    if (this._isGeneratingScales) {
      return;
    }

    // 缓存优化：如果时间变化很小且不是强制重新生成，跳过重新生成
    const { currentTime } = this.data;
    if (
      !forceRegenerate &&
      this._lastScaleGenerateTime &&
      Math.abs(currentTime - this._lastScaleGenerateTime) < 100
    ) {
      return; // 时间变化小于100ms，跳过重新生成
    }

    this._isGeneratingScales = true;
    this._lastScaleGenerateTime = currentTime;

    try {
      const {
        baseTime,
        pixelsPerSecond,
        containerWidth,
        scaleUnit,
        timeRangeStart,
        timeRangeEnd,
      } = this.data;

      // 参数验证
      if (
        !baseTime ||
        !pixelsPerSecond ||
        !containerWidth ||
        containerWidth <= 0
      ) {
        console.warn("generateTimeScales: 参数无效", {
          baseTime,
          pixelsPerSecond,
          containerWidth,
        });
        this._isGeneratingScales = false;
        return;
      }

      // 防止除零错误
      if (pixelsPerSecond <= 0) {
        console.warn("generateTimeScales: pixelsPerSecond 必须大于0");
        this._isGeneratingScales = false;
        return;
      }

      // 根据刻度单位确定显示范围和间隔
      const scaleConfig = this._getScaleConfig(scaleUnit);

      // 基于当前时间生成刻度范围，但位置计算基于baseTime实现移动效果
      const displayTimeRange = (containerWidth / pixelsPerSecond) * 1000; // 显示的时间范围（毫秒）
      const halfRange = displayTimeRange / 2;

      // 使用currentTime作为中心来生成刻度范围
      const { currentTime } = this.data;
      const centerTime = currentTime || baseTime; // 如果currentTime不存在则使用baseTime

      const startTime = centerTime - halfRange;
      const endTime = centerTime + halfRange;

      const timeScales = [];

      // 对齐到刻度间隔的边界，确保刻度整齐
      let alignedStartTime =
        Math.floor(startTime / scaleConfig.interval) * scaleConfig.interval;
      const alignedEndTime =
        Math.ceil(endTime / scaleConfig.interval) * scaleConfig.interval;

      // 应用时间范围限制
      if (timeRangeStart && alignedStartTime < timeRangeStart) {
        alignedStartTime =
          Math.ceil(timeRangeStart / scaleConfig.interval) *
          scaleConfig.interval;
      }

      // 获取当前真实时间用于判断未来时间刻度
      const realCurrentTime = Date.now();

      // 虚拟化优化：只生成可见区域及缓冲区的刻度
      const bufferZone = containerWidth * 0.2; // 20%的缓冲区
      const visibleStartTime =
        centerTime -
        ((containerWidth / 2 + bufferZone) / pixelsPerSecond) * 1000;
      const visibleEndTime =
        centerTime +
        ((containerWidth / 2 + bufferZone) / pixelsPerSecond) * 1000;

      // 调整生成范围到可见区域
      const virtualizedStartTime = Math.max(
        alignedStartTime,
        Math.floor(visibleStartTime / scaleConfig.interval) *
          scaleConfig.interval,
      );
      const virtualizedEndTime = Math.min(
        alignedEndTime,
        Math.ceil(visibleEndTime / scaleConfig.interval) * scaleConfig.interval,
      );

      // 根据刻度单位生成刻度点（虚拟化版本）
      for (
        let time = virtualizedStartTime;
        time <= virtualizedEndTime;
        time += scaleConfig.interval
      ) {
        const date = new Date(time);
        const scaleInfo = this._getScaleInfo(date, scaleUnit);

        // 实现轨道移动效果的位置计算
        const centerPosition = containerWidth / 2;
        // 基准线固定在中心，刻度相对于当前时间的位置
        const relativeTime = (time - centerTime) / 1000; // 相对于当前时间的偏移
        const position = centerPosition + relativeTime * pixelsPerSecond;

        // 跳过不在可见区域的刻度（双重检查）
        if (position < -bufferZone || position > containerWidth + bufferZone) {
          continue;
        }

        // 根据刻度类型决定是否显示时间文本
        let timeText = "";
        if (scaleInfo.showText) {
          timeText = this._formatTimeText(date, scaleUnit);
        }

        // 判断是否为未来时间刻度
        const isFuture = time > realCurrentTime;

        timeScales.push({
          time,
          position,
          scaleType: scaleInfo.type,
          timeText,
          isFuture,
        });
      }

      // 暂时禁用验证处理，避免只读错误
      // const validatedScales = this._validateAndCleanScales(timeScales);

      this.setData({ timeScales });
      this._updateRecordArea();
    } catch (error) {
      console.error("生成刻度异常:", error);
      // 异常时设置空刻度数组，避免页面崩溃
      this.setData({ timeScales: [] });
    } finally {
      // 重置生成标志，允许下次生成
      this._isGeneratingScales = false;
    }
  },

  /**
   * 同步的刻度生成核心逻辑，从generateTimeScales中分离出来
   * @param {string} forceScaleUnit 强制使用的刻度单位，用于解决快速切换时的时序问题
   */
  _generateTimeScalesSync: function (forceScaleUnit) {
    const {
      baseTime,
      pixelsPerSecond,
      containerWidth,
      scaleUnit: dataScaleUnit,
      timeRangeStart,
      timeRangeEnd,
    } = this.data;

    // 使用强制刻度单位或数据中的刻度单位
    const scaleUnit = forceScaleUnit || dataScaleUnit;

    // 参数验证
    if (
      !baseTime ||
      !pixelsPerSecond ||
      !containerWidth ||
      containerWidth <= 0
    ) {
      console.warn("generateTimeScales: 参数无效", {
        baseTime,
        pixelsPerSecond,
        containerWidth,
      });
      return;
    }

    // 防止除零错误
    if (pixelsPerSecond <= 0) {
      console.warn("generateTimeScales: pixelsPerSecond 必须大于0");
      return;
    }

    // 根据刻度单位确定显示范围和间隔
    const scaleConfig = this._getScaleConfig(scaleUnit);

    // 基于当前时间生成刻度范围，但位置计算基于baseTime实现移动效果
    const displayTimeRange = (containerWidth / pixelsPerSecond) * 1000; // 显示的时间范围（毫秒）
    const halfRange = displayTimeRange / 2;

    // 使用currentTime作为中心来生成刻度范围
    const { currentTime } = this.data;
    const centerTime = currentTime || baseTime; // 如果currentTime不存在则使用baseTime

    const startTime = centerTime - halfRange;
    const endTime = centerTime + halfRange;

    const timeScales = [];

    // 对齐到刻度间隔的边界，确保刻度整齐
    let alignedStartTime =
      Math.floor(startTime / scaleConfig.interval) * scaleConfig.interval;
    const alignedEndTime =
      Math.ceil(endTime / scaleConfig.interval) * scaleConfig.interval;

    // 应用时间范围限制
    if (timeRangeStart && alignedStartTime < timeRangeStart) {
      alignedStartTime =
        Math.ceil(timeRangeStart / scaleConfig.interval) * scaleConfig.interval;
    }

    // 获取当前真实时间用于判断未来时间刻度
    const realCurrentTime = Date.now();

    // 虚拟化优化：只生成可见区域及缓冲区的刻度
    const bufferZone = containerWidth * 0.2; // 20%的缓冲区
    const visibleStartTime =
      centerTime - ((containerWidth / 2 + bufferZone) / pixelsPerSecond) * 1000;
    const visibleEndTime =
      centerTime + ((containerWidth / 2 + bufferZone) / pixelsPerSecond) * 1000;

    // 调整生成范围到可见区域
    const virtualizedStartTime = Math.max(
      alignedStartTime,
      Math.floor(visibleStartTime / scaleConfig.interval) *
        scaleConfig.interval,
    );
    const virtualizedEndTime = Math.min(
      alignedEndTime,
      Math.ceil(visibleEndTime / scaleConfig.interval) * scaleConfig.interval,
    );

    // 根据刻度单位生成刻度点（虚拟化版本）
    for (
      let time = virtualizedStartTime;
      time <= virtualizedEndTime;
      time += scaleConfig.interval
    ) {
      const date = new Date(time);
      const scaleInfo = this._getScaleInfo(date, scaleUnit);

      // 实现轨道移动效果的位置计算
      const centerPosition = containerWidth / 2;
      // 基准线固定在中心，刻度相对于当前时间的位置
      const relativeTime = (time - centerTime) / 1000; // 相对于当前时间的偏移
      const position = centerPosition + relativeTime * pixelsPerSecond;

      // 跳过不在可见区域的刻度（双重检查）
      if (position < -bufferZone || position > containerWidth + bufferZone) {
        continue;
      }

      // 根据刻度类型决定是否显示时间文本
      let timeText = "";
      if (scaleInfo.showText) {
        timeText = this._formatTimeText(date, scaleUnit);
      }

      // 判断是否为未来时间刻度
      const isFuture = time > realCurrentTime;

      timeScales.push({
        time,
        position,
        scaleType: scaleInfo.type,
        timeText,
        isFuture,
      });
    }

    // 暂时禁用验证处理，避免只读错误
    // const validatedScales = this._validateAndCleanScales(timeScales);

    this.setData({ timeScales });
    this._updateRecordArea();
  },

  // 判断指定时间是否在录制区域内
  async getCameraList() {
    try {
      // 获取设备列表
      const deviceListRes = await querySitDevLivingListApi({
        siteCode: this.data.siteDetail.siteCode,
        pageNo: 1,
        pageSize: 100,
      });
      this.setData({
        cameraList: deviceListRes.data.list,
        videoUrl: deviceListRes.data.list[0].liveUrl,
      });
    } catch (e) {
      this.errorAndReturn();
    }
  },
  // 开始边缘自动滚动 - 重构版本：固定滚动速度，移动轨道，实时更新录制区域
  /**
   * 获取录像列表（优化版本）
   * @returns {Array} 录像列表
   */
  async getVideoList() {
    // 防止重复调用
    if (this._isLoadingVideoList) {
      return this.data.videoList || [];
    }

    // 验证摄像头列表
    if (!this.data.cameraList || this.data.cameraList.length === 0) {
      console.warn("摄像头列表为空，无法获取录像列表");
      this.setData({ videoList: [] });
      return [];
    }

    // 验证当前激活的摄像头
    const currentCamera = this.data.cameraList[this.data.activateCameraIndex];
    if (!currentCamera || !currentCamera.id) {
      console.warn("未选择有效设备，无法获取录像列表");
      this.setData({ videoList: [] });
      return [];
    }

    const devId = currentCamera.id;
    this._isLoadingVideoList = true;

    // 记录当前请求的设备ID和站点ID，用于检查请求是否过期
    const currentRequestDevId = devId;
    const currentRequestSiteId = this.data.siteDetail?.id;

    try {
      const requestParams = {
        siteId: currentRequestSiteId,
        current: 1,
        size: 1000,
      };

      const res = await slicingTaskPage(requestParams);

      // 检查请求是否过期（用户可能已经切换了摄像头或站点）
      const currentDevId = this.data.cameraList[this.data.activateCameraIndex]?.id;
      const currentSiteId = this.data.siteDetail?.id;

      if (currentRequestDevId !== currentDevId || currentRequestSiteId !== currentSiteId) {
        return this.data.videoList || [];
      }

      const videoList = this._handleVideoListResponseOptimized(res);
      return videoList;
    } catch (error) {
      console.error("获取录像列表失败:", error);

      // 只有在请求没有过期的情况下才处理错误
      const currentDevId = this.data.cameraList[this.data.activateCameraIndex]?.id;
      const currentSiteId = this.data.siteDetail?.id;

      if (currentRequestDevId === currentDevId && currentRequestSiteId === currentSiteId) {
        return this._handleVideoListError(error);
      }

      return this.data.videoList || [];
    } finally {
      this._isLoadingVideoList = false;
    }
  },
  // 停止边缘自动滚动
  handleRecordAreaDragEnd: async function (e) {
    if (!this.data.isRecordAreaDragging) return;

    // 保存当前的拖拽类型，因为后面会被重置
    const currentDragType = this.data.recordDragType;

    // 保存拖拽前的状态（从拖拽开始时保存的原始状态）
    const beforeState = {
      recordStartTime: this.data.recordDragOriginalStartTime,
      recordEndTime: this.data.recordDragOriginalEndTime,
      recordStartTimeFormatted: this.formatTime(
        this.data.recordDragOriginalStartTime,
      ),
      recordEndTimeFormatted: this.formatTime(
        this.data.recordDragOriginalEndTime,
      ),
      recordDurationText: this.formatDuration(
        (this.data.recordDragOriginalEndTime -
          this.data.recordDragOriginalStartTime) /
          1000,
      ),
      baseTime: this.data.baseTime,
      currentTime: this.data.currentTime,
      pausedTime: this.data.pausedTime,
      trackOffset: this.data.trackOffset,
    };

    // 停止边缘自动滚动（如果有的话）
    this._stopEdgeScrolling();

    // 使用松手位置作为最终手柄位置
    const finalHandleX = e.changedTouches[0].clientX;

    // 根据最终位置计算时间并吸附到刻度
    const { containerWidth, currentTime, pixelsPerSecond } = this.data;

    // 使用与刻度生成相同的逻辑：相对于currentTime计算
    const centerTime = currentTime || this.data.baseTime;
    const centerPosition = containerWidth / 2;
    const relativePosition = finalHandleX - centerPosition;
    let handlerTime = centerTime + (relativePosition / pixelsPerSecond) * 1000;

    // 严格的边界检查
    const realCurrentTime = Date.now();
    const { leftBoundaryTime } = this.data;

    // 左侧手柄边界限制：不能早于leftBoundaryTime
    if (currentDragType === "start") {
      if (leftBoundaryTime && handlerTime < leftBoundaryTime) {
        handlerTime = leftBoundaryTime;
      }
    }

    // 右侧手柄边界限制：不能晚于当前真实时间
    if (currentDragType === "end") {
      if (handlerTime > realCurrentTime) {
        handlerTime = realCurrentTime;
      }
    }

    // 将时间吸附到最近的刻度
    const snappedHandlerTime = this._calculateNearestScaleTime(handlerTime);

    // 计算最小6个刻度的时间间隔
    const scaleInterval = this._getScaleConfig(this.data.scaleUnit).interval;
    const minDuration = 6 * scaleInterval; // 最小6个刻度的时间长度

    // 获取当前录制区域时间
    let newStartTime = this.data.recordStartTime;
    let newEndTime = this.data.recordEndTime;
    const currentDuration = newEndTime - newStartTime;

    // 根据拖拽类型更新对应的时间，并应用严格的六个刻度限制
    if (currentDragType === "start") {
      let proposedStartTime = snappedHandlerTime;

      // 再次检查左边界限制
      if (leftBoundaryTime && proposedStartTime < leftBoundaryTime) {
        proposedStartTime = leftBoundaryTime;
      }

      // 严格的六个刻度限制逻辑
      if (currentDuration <= minDuration) {
        // 当前已经是最小范围（六个刻度），只允许向外拖拽（扩大区域）
        if (proposedStartTime > newStartTime) {
          // 向右拖拽（缩小区域），保持在当前位置，不允许缩小
          proposedStartTime = newStartTime;
        }
        // 向左拖拽（扩大区域）允许，使用proposedStartTime
      } else {
        // 当前大于最小范围，检查是否会小于六个刻度
        if (newEndTime - proposedStartTime < minDuration) {
          // 会小于六个刻度，固定在六个刻度的边界位置
          proposedStartTime = newEndTime - minDuration;
        }
        // 否则允许自由拖拽
      }

      newStartTime = proposedStartTime;
    } else if (currentDragType === "end") {
      let proposedEndTime = snappedHandlerTime;

      // 再次检查右边界限制
      if (proposedEndTime > realCurrentTime) {
        proposedEndTime = realCurrentTime;
      }

      // 严格的六个刻度限制逻辑
      if (currentDuration <= minDuration) {
        // 当前已经是最小范围（六个刻度），只允许向外拖拽（扩大区域）
        if (proposedEndTime < newEndTime) {
          // 向左拖拽（缩小区域），保持在当前位置，不允许缩小
          proposedEndTime = newEndTime;
        }
        // 向右拖拽（扩大区域）允许，使用proposedEndTime
      } else {
        // 当前大于最小范围，检查是否会小于六个刻度
        if (proposedEndTime - newStartTime < minDuration) {
          // 会小于六个刻度，固定在六个刻度的边界位置
          proposedEndTime = newStartTime + minDuration;
        }
        // 否则允许自由拖拽
      }

      newEndTime = proposedEndTime;
    }

    // 更新录制区域时间
    this.setData({
      recordStartTime: newStartTime,
      recordEndTime: newEndTime,
      recordStartTimeFormatted: this.formatTime(newStartTime),
      recordEndTimeFormatted: this.formatTime(newEndTime),
      isRecordAreaDragging: false,
      recordDragType: "",
      recordDragStartX: 0,
    });

    // 更新录制时长文本
    this._updateRecordDurationText();

    // 清理拖拽追踪器和缓存
    this._dragTracker = null;
    this._cachedBaseData = null;
    this._formatUpdatePending = false;

    // 手柄拖拽后只暂停播放，不移动基准线
    this.setData({
      isPlaying: false, // 暂停播放
    });

    // 更新录制区域显示
    this._updateRecordArea(true);

    // 保存拖拽后的状态到历史记录
    const afterState = {
      recordStartTime: newStartTime,
      recordEndTime: newEndTime,
      recordStartTimeFormatted: this.formatTime(newStartTime),
      recordEndTimeFormatted: this.formatTime(newEndTime),
      recordDurationText: this.data.recordDurationText,
      baseTime: this.data.baseTime,
      currentTime: this.data.currentTime,
      pausedTime: this.data.pausedTime,
      trackOffset: this.data.trackOffset,
    };

    // 只有当录制区域真正发生变化时才保存历史记录
    if (
      beforeState.recordStartTime !== afterState.recordStartTime ||
      beforeState.recordEndTime !== afterState.recordEndTime
    ) {
      const dragType = currentDragType === "start" ? "开始" : "结束";
    }
  },
  // ==================== 撤回功能相关方法 ====================

  // 保存操作到历史记录
  handleRecordAreaDragMove: function (e) {
    if (!this.data.isRecordAreaDragging) {
      return;
    }

    const currentX = e.touches[0].clientX;

    // 取消边缘滚动逻辑，只在一屏内拖拽
    // 停止任何正在进行的边缘滚动
    if (this.data.isEdgeScrolling) {
      this._stopEdgeScrolling();
    }

    // 直接根据手指位置实时更新录制区域时间，不限制边缘
    this._updateRecordTimeByHandlePositionUltraOptimized(currentX);
  },
  // 撤回上一步操作 - 只有用户有剪辑动作才能撤回
  handleRecordAreaDragStart: function (e) {
    if (!this.data.isRecording) {
      return;
    }

    const touchX = e.touches[0].clientX;
    const touchY = e.touches[0].clientY;
    const dragType = e.currentTarget.dataset.type; // 'start' 或 'end'

    // 停止轨道的自动播放
    this.stopAutoPlay();

    // 初始化拖拽追踪器
    this._dragTracker = null;
    this._cachedBaseData = null;
    this._formatUpdatePending = false;

    this.setData({
      isRecordAreaDragging: true,
      recordDragType: dragType,
      recordDragStartX: touchX,
      handleDragStartY: touchY, // 记录Y坐标，用于限制只在X轴拖拽
      isPlaying: false,
      // 保存拖拽开始时的原始时间，避免累积误差
      recordDragOriginalStartTime: this.data.recordStartTime,
      recordDragOriginalEndTime: this.data.recordEndTime,
    });
  },
  /**
   * 处理录制完成（提交切片任务）
   */
  async handleRecordComplete() {
    const { recordStartTime, recordEndTime } = this.data;

    if (!recordStartTime || !recordEndTime) {
      wx.showToast({
        title: "录制时间信息错误",
        icon: "none",
      });
      return;
    }

    const startTime = Math.min(recordStartTime, recordEndTime);
    const endTime = Math.max(recordStartTime, recordEndTime);

    wx.showLoading({
      title: "正在提交切片任务...",
      mask: true,
    });

    try {
      const devId = this.data.cameraList[this.data.activateCameraIndex].id;

      const { code, data, msg } = await submitSlicingTask({
        id: devId,
        siteId: this.data.siteDetail.id,
        startTime: Math.floor(startTime / 1000),
        endTime: Math.floor(endTime / 1000),
      });

      if (code === 0) {
        wx.showToast({
          title: "切片任务提交成功",
          icon: "success",
          duration: 2000,
        });

        // 重置剪辑状态
        this._resetClippingState();

        // 刷新权益和录像列表
        await Promise.all([this.checkUserRights(), this.getVideoList()]);
      } else {
        // 提交失败时也要重置剪辑状态
        this._resetClippingState();
        // 重新检查权益状态
        await this.checkUserRights();

        wx.showToast({
          title: msg || "提交失败",
          icon: "none",
        });
      }
      this.playVideo();
    } catch (error) {
      console.error("提交切片任务失败:", error);
      // 提交失败时也要重置剪辑状态
      this._resetClippingState();

      wx.showToast({
        title: "网络错误，请重试",
        icon: "none",
        duration: 2000,
      });
    } finally {
      wx.hideLoading();
    }
  },
  // 触摸结束处理 - 支持拖拽
  handleTouchEnd: function (e) {
    // 如果正在拖拽录制区域手柄，不处理其他操作
    if (this.data.isRecordAreaDragging) {
      return;
    }

    if (this.data.isDragging) {
      // 结束单指拖拽
      this._handleDragEnd();
    }
  },
  // 触摸移动处理 - 支持拖拽
  handleTouchMove: function (e) {
    // 如果正在拖拽录制区域手柄，不处理其他操作
    if (this.data.isRecordAreaDragging) {
      return;
    }

    const touches = e.touches;
    const touchCount = touches.length;

    if (touchCount === 1 && this.data.isDragging) {
      // 单指拖拽移动
      this._handleDragMove(e);
    }
  },
  // 触摸事件处理 - 支持拖拽
  handleTouchStart: function (e) {
    const touches = e.touches;
    const touchCount = touches.length;

    if (touchCount === 1) {
      // 单指拖拽开始
      this._handleDragStart(e);
    }
  },
  // 撤回重置录制区域操作
  handleTutorialHighlightTap: function (e) {
    const target = e.currentTarget.dataset.target;
    const currentStep = this.data.tutorialStep;
    switch (target) {
      case "clip-button":
        // 第一步：剪辑按钮提示
        wx.showToast({
          title: "点击这里可以创建录制区域",
          icon: "none",
          duration: 1500,
        });

        // 延迟自动进入下一步
        setTimeout(() => {
          this.nextTutorialStep();
        }, 1800);
        break;
      case "track-container-box":
        // 第二步：手势区域提示
        wx.showToast({
          title: "在此区域可拖拽手柄、双指缩放、左右滑动",
          icon: "none",
          duration: 2000,
        });
        // 延迟自动进入下一步
        setTimeout(() => {
          this.nextTutorialStep();
        }, 2200);
        break;
      case "save-slice-btn":
      case "save-button":
        // 第三步：保存按钮提示
        wx.showToast({
          title: "点击这里可以保存录制片段",
          icon: "none",
          duration: 1500,
        });
        // 延迟完成教程
        setTimeout(() => {
          this._completeTutorial();
        }, 1800);
        break;
    }
  },
  // ==================== 新手教程相关方法 ====================

  // 检查并显示新手教程
  /**
   * 隐藏删除按钮
   */
  hideDeleteButton() {
    if (this.data.swipeDeleteItemId) {
      this.setData({
        swipeDeleteItemId: null,
      });
    }
  },

  /**
   * 检查录制区域是否可能在当前时间范围内可见
   */
  _isRecordAreaPotentiallyVisible: function () {
    const {
      recordStartTime,
      recordEndTime,
      timeScales,
      baseTime,
      pixelsPerSecond,
      containerWidth,
    } = this.data;

    if (!recordStartTime || !recordEndTime || !timeScales.length) {
      return false;
    }

    // 计算当前可视时间范围
    const halfTime = ((containerWidth / pixelsPerSecond) * 1000) / 2; // 一半屏幕对应的时间
    const visibleStartTime = baseTime - halfTime;
    const visibleEndTime = baseTime + halfTime;

    // 检查录制区域是否与可视时间范围有重叠
    const hasOverlap = !(
      recordEndTime < visibleStartTime || recordStartTime > visibleEndTime
    );

    return hasOverlap;
  },

  /**
   * 与 DOM 同步更新录制区域 - 确保录制区域跟着 DOM 刻度正确绘制
   */
  _updateRecordAreaWithDOMSync: function () {
    const maxRetries = 5;
    let retryCount = 0;

    const tryUpdate = () => {
      // 使用 wx.nextTick 确保数据更新完成
      wx.nextTick(() => {
        // 再使用 setTimeout 确保 DOM 渲染完成
        setTimeout(() => {
          // 检查刻度是否已经渲染到 DOM
          const query = wx.createSelectorQuery().in(this);
          query
            .selectAll(".time-scale-item")
            .boundingClientRect((rects) => {
              if (rects && rects.length > 0) {
                // 更新录制区域
                this._updateRecordArea();

                // 检查录制区域是否在可视范围内
                const isVisible = this._isRecordAreaPotentiallyVisible();
                if (!isVisible) {
                  wx.showToast({
                    title: "录制区域不在当前时间范围内",
                    icon: "none",
                    duration: 2000,
                  });
                }
              } else if (retryCount < maxRetries) {
                setTimeout(tryUpdate, 50 * retryCount); // 递增延迟
              } else {
                // 重试次数用完，强制更新
                console.warn("DOM 刻度渲染超时，强制更新录制区域");
                this._updateRecordArea();
              }
            })
            .exec();
        }, 50); // 基础延迟
      });
    };

    tryUpdate();
  },

  /**
   * 强制图片更新 - 仅在数据更新时使用，添加时间戳破坏缓存
   * @param {Array} imageUpdates 需要更新图片的项目列表
   */
  _forceImageRerender(imageUpdates) {
    if (!imageUpdates || imageUpdates.length === 0) return;

    const updateData = {};
    imageUpdates.forEach((item) => {
      const listIndex = this.data.videoList.findIndex((v) => v.id === item.id);
      if (listIndex >= 0 && item.coverKey) {
        // 只有在真正需要强制刷新时才添加时间戳
        const timestamp = Date.now();
        const cleanUrl = item.coverKey.split("?")[0];
        const newThumbnail = `${cleanUrl}?t=${timestamp}`;
        updateData[`videoList[${listIndex}].thumbnail`] = newThumbnail;
      }
    });

    if (Object.keys(updateData).length > 0) {
      this.setData(updateData);
    }
  },

  /**
   * 跳转到视频播放页面（带节流）
   * @param {string} videoUrl 视频URL
   * @param {string} videoId 视频ID
   */
  navigateToVideoPlayWithThrottle(videoUrl, videoId) {
    // 节流检查：防止快速重复点击
    if (this._isNavigatingToVideo) {
      return;
    }

    if (!videoUrl) {
      wx.showToast({
        title: "视频链接无效",
        icon: "none",
      });
      return;
    }

    // 设置节流标志
    this._isNavigatingToVideo = true;

    // 显示跳转提示
    wx.showLoading({
      title: "正在跳转...",
      mask: true,
    });

    wx.navigateTo({
      url: `/pages/common/play/index?videoUrl=${encodeURIComponent(videoUrl)}&videoId=${videoId}`,
      success: () => {
        wx.hideLoading();
      },
      fail: (error) => {
        console.error("跳转视频播放页面失败:", error);
        wx.hideLoading();
        wx.showToast({
          title: "跳转失败",
          icon: "none",
        });
      },
      complete: () => {
        // 延迟重置节流标志，防止页面返回时立即可点击
        setTimeout(() => {
          this._isNavigatingToVideo = false;
        }, 1000);
      },
    });
  },

  /**
   * 跳转到视频播放页面（原方法保留，供其他地方调用）
   * @param {string} videoUrl 视频URL
   * @param {string} videoId 视频ID
   */
  navigateToVideoPlay(videoUrl, videoId) {
    if (!videoUrl) {
      wx.showToast({
        title: "视频链接无效",
        icon: "none",
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/common/play/index?videoUrl=${encodeURIComponent(videoUrl)}&videoId=${videoId}`,
      fail: (error) => {
        console.error("跳转视频播放页面失败:", error);
        wx.showToast({
          title: "跳转失败",
          icon: "none",
        });
      },
    });
  },
  // 下一步教程
  nextTutorialStep: function () {
    const currentStep = this.data.tutorialStep;

    if (currentStep < 3) {
      // 如果是第一步，点击下一步时创建真实的录制区域
      if (currentStep === 1) {
        this.toggleRecord(); // 调用真实的剪辑方法
      }

      // 进入下一步
      this.setData(
        {
          tutorialStep: currentStep + 1,
          tutorialHighlightReady: false, // 重置高亮框状态
        },
        () => {
          // 切换步骤后重新计算高亮框位置
          wx.nextTick(() => {
            setTimeout(() => {
              this._updateTutorialHighlightPosition();
            }, 100); // 给一点时间让DOM更新
          });
        },
      );
    } else {
      // 完成教程
      this._completeTutorial();
    }
  },
  // 跳过教程
  notOnlineFunction: function () {
    wx.showToast({
      title: "功能未上线，敬请期待！",
      icon: "none",
      duration: 2000,
    });
  },
  // 处理教程高亮区域点击
  /**
   * 删除录像项
   * @param {Object} e 事件对象
   */
  onDeleteVideoItem(e) {
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }

    const { id } = e.currentTarget.dataset;
    const videoItem = this.data.videoList.find((item) => item.id === id);

    if (!videoItem) return;

    wx.showModal({
      title: "确认删除",
      content: `确定要删除录像"${videoItem.name}"吗？`,
      confirmText: "删除",
      cancelText: "取消",
      confirmColor: "#ff4444",
      success: (res) => {
        if (res.confirm) {
          this.deleteVideoItem(id);
        }
        this.setData({
          swipeDeleteItemId: null,
        });
      },
    });
  },
  // 完成教程
  async onLoad(options) {
    try {
      // 设置页面方向为竖屏并禁止滚动
      try {
        wx.setPageOrientation({
          orientation: "portrait",
        });
      } catch (e) {
        console.log("设置页面方向失败:", e);
      }

      // 禁止页面滚动
      try {
        wx.pageScrollTo({
          scrollTop: 0,
          duration: 0,
        });
      } catch (e) {
        console.log("设置页面滚动失败:", e);
      }

      // 场馆
      await this._initPageData(options);
      // 摄像头列表
      await this.getCameraList();
      // 用户权益
      await this.checkUserRights();
      // 切片列表
      await this.getVideoList();
      // 剪辑页相关
      await this._initClipping();
      // 视频开始播放
      this.playVideo();
      // 延迟启动轮询，避免与初始化冲突
      setTimeout(() => {
        this.startDevicePolling(this.data.siteDetail.siteCode);
      }, 1000);
    } catch (error) {
      console.error("页面初始化失败:", error);
      // 如果初始化失败，显示错误提示并返回上一页
      this.errorAndReturn();
    }
  },
  /**
   * 页面显示时的处理
   * 延迟获取容器宽度，更新基准线位置，在剪辑状态下重新生成刻度
   */
  onShow: function () {
    // 延迟一点时间确保页面完全渲染
    setTimeout(async () => {
      await this._getContainerWidth();
      // 更新基准线位置
      this._updateBaselinePosition();
      // 只有在剪辑状态下才重新生成刻度
      if (this.data.isClipping) {
        this.generateTimeScales();
        // 如果在录制中，更新录制区域
        if (this.data.isRecording) {
          this._updateRecordArea();
        }
      }
    }, 100);
  },
  // 输出教程调试信息
  /**
   * 缩略图加载完成处理 - 简化版本
   * @param {Object} e 事件对象
   */
  onThumbnailLoad(e) {
    const { id, index } = e.currentTarget.dataset;
    // 缩略图加载成功，无需特殊处理
    // 图片会自然显示，不需要复杂的状态管理
  },

  /**
   * 缩略图加载失败处理 - 简化版本
   * @param {Object} e 事件对象
   */
  onThumbnailError(e) {
    const { id, index } = e.currentTarget.dataset;
    const videoList = this.data.videoList;

    console.warn(`缩略图加载失败 - ID: ${id}, Index: ${index}`);
    console.warn(`当前缩略图URL: ${videoList[index]?.thumbnail}`);

    // 验证索引和ID匹配，直接使用兜底图
    if (videoList[index] && videoList[index].id === id) {
      console.warn(`设置兜底图 - ID: ${id}`);
      this.setData({
        [`videoList[${index}].thumbnail`]: "/images/live/slice.png",
      });
    } else {
      console.error(`索引或ID不匹配 - Index: ${index}, ID: ${id}, 实际ID: ${videoList[index]?.id}`);
    }
  },
  // 重置教程状态（用于测试）
  onUnload: function () {
    // 停止自动播放
    this.stopAutoPlay();
    // 停止惯性动画
    this._stopInertiaAnimation();
    // 停止刻度颜色更新定时器
    this._stopScaleColorTimer();
    // 清理速度追踪
    this.velocityTracker = null;
    this.stopAutoPlay();
    this._stopEdgeScrolling(); // 确保清理边缘滚动定时器
    // 清理轮询定时器
    this._clearAllTimers();
    // 清理视频预览定时器
    if (this._videoPreviewTimer) {
      clearTimeout(this._videoPreviewTimer);
      this._videoPreviewTimer = null;
    }
    // 清理缩放提示定时器
    if (this._zoomToastTimer) {
      clearTimeout(this._zoomToastTimer);
      this._zoomToastTimer = null;
    }
    // 清理缩放节流定时器
    if (this._zoomThrottleTimer) {
      clearTimeout(this._zoomThrottleTimer);
      this._zoomThrottleTimer = null;
    }
    // 重置视频更新状态
    this._isUpdatingVideo = false;
    this._lastVideoTimestamp = null;
  },
  // 初始化剪辑相关配置
  /**
   * 录像项触摸结束
   * @param {Object} e 触摸事件
   */
  onVideoItemTouchEnd(e) {
    const { touchState } = this.data;
    const touchDuration = Date.now() - touchState.startTime;
    const isMoved = touchState.moved;

    // 重置触摸状态
    this.setData({
      touchState: {
        startTime: null,
        startX: null,
        startY: null,
        moved: false,
      },
    });

    // 如果是移动或长按，不处理点击事件
    if (isMoved || touchDuration >= 200) return;

    const { url, id } = e.currentTarget.dataset;

    // 处理删除按钮状态
    if (this.data.swipeDeleteItemId === id || this.data.swipeDeleteItemId) {
      this.setData({ swipeDeleteItemId: null });
      return;
    }

    // 检查视频状态并跳转播放（添加节流机制）
    const videoItem = this.data.videoList.find((item) => item.id === id);
    if (videoItem?.sliceStatue === 20) {
      this.navigateToVideoPlayWithThrottle(url, id);
    }
  },
  // ==================== 视频控制相关 ====================

  /**
   * 录像项触摸移动
   * @param {Object} e 触摸事件
   */
  onVideoItemTouchMove(e) {
    const { touchState } = this.data;
    if (!touchState.startX || !touchState.startY) return;

    const deltaX = e.touches[0].clientX - touchState.startX;
    const deltaY = e.touches[0].clientY - touchState.startY;
    const absDeltaX = Math.abs(deltaX);
    const absDeltaY = Math.abs(deltaY);

    // 标记为已移动
    if (!touchState.moved && (absDeltaX > 5 || absDeltaY > 5)) {
      this.setData({
        "touchState.moved": true,
      });
    }

    // 水平滑动处理删除按钮
    if (absDeltaX > 5 && absDeltaX > absDeltaY) {
      const { id } = e.currentTarget.dataset;

      if (deltaX < -30) {
        this.setData({ swipeDeleteItemId: id });
      } else if (deltaX > 15 && this.data.swipeDeleteItemId === id) {
        this.setData({ swipeDeleteItemId: null });
      }
    }
  },
  // ==================== 轮询和定时器管理 ====================

  /**
   * 录像项触摸开始
   * @param {Object} e 触摸事件
   */
  onVideoItemTouchStart(e) {
    this.setData({
      touchState: {
        startTime: Date.now(),
        startX: e.touches[0].clientX,
        startY: e.touches[0].clientY,
        moved: false,
      },
    });
  },
  playVideo() {
    wx.nextTick(function () {
      const videoContext = wx.createVideoContext("myVideo", this);
      videoContext.play();
    });
  },
  /**
   * 轮询摄像头列表
   * @param {string} siteCode 站点编码
   */
  async pollCameraList(siteCode) {
    try {
      const res = await querySitDevLivingListApi({
        siteCode: siteCode,
        pageNo: 1,
        pageSize: 100,
      });

      const newList = res.data.list;
      const needUpdate = this._checkIfCameraNeedUpdate(newList);

      if (needUpdate) {
        this._updateCameraList(newList);
      }
    } catch (error) {
      console.error("轮询摄像头列表失败:", error);
    }
  },
  /**
   * 轮询录像列表（优化版本）
   */
  async pollVideoList() {
    // 如果正在加载或录制中，跳过轮询
    if (this._isLoadingVideoList || this.data.isRecording) {
      return;
    }

    // 检查轮询频率，避免过于频繁
    const now = Date.now();
    if (this._lastPollTime && (now - this._lastPollTime) < 2000) {
      return;
    }

    this._lastPollTime = now;

    try {
      await this.getVideoList();
    } catch (error) {
      console.error("轮询录像列表失败:", error);
      // 轮询失败时不显示错误提示，避免干扰用户
    }
  },

  /**
   * 重试切片任务 - 优化版本，增强边界情况处理
   * @param {Object} e 事件对象
   */
  async retrySlicingTask(e) {
    // 防止重复提交
    if (this._isRetrying) {
      console.log("重试操作正在进行中，跳过重复调用");
      return;
    }

    // 检查事件对象是否存在
    if (!e || !e.currentTarget || !e.currentTarget.dataset) {
      console.error("retrySlicingTask: 事件对象或dataset不存在", e);
      wx.showToast({
        title: "参数错误，无法重试",
        icon: "none",
      });
      return;
    }

    const dataset = e.currentTarget.dataset;
    const id = dataset.id;
    const startTime = dataset.startTime || dataset["start-time"];
    const endTime = dataset.endTime || dataset["end-time"];

    // 参数验证
    if (!id || !startTime || !endTime) {
      console.error("retrySlicingTask: 缺少必要参数", {
        id,
        startTime,
        endTime,
        dataset
      });
      wx.showToast({
        title: "参数错误，无法重试",
        icon: "none",
      });
      return;
    }

    // 验证摄像头和站点信息
    if (!this.data.cameraList || this.data.cameraList.length === 0) {
      console.error("retrySlicingTask: 摄像头列表为空");
      wx.showToast({
        title: "摄像头信息异常，请刷新页面",
        icon: "none",
      });
      return;
    }

    const currentCamera = this.data.cameraList[this.data.activateCameraIndex];
    if (!currentCamera || !currentCamera.id) {
      console.error("retrySlicingTask: 当前摄像头无效");
      wx.showToast({
        title: "当前摄像头无效，请重新选择",
        icon: "none",
      });
      return;
    }

    if (!this.data.siteDetail || !this.data.siteDetail.id) {
      console.error("retrySlicingTask: 站点信息无效");
      wx.showToast({
        title: "站点信息异常，请刷新页面",
        icon: "none",
      });
      return;
    }

    // 检查权益（如果有权益检查功能）
    if (typeof this.checkUserRights === 'function') {
      try {
        const rights = await this.checkUserRights();
        if (!rights || rights.remain <= 0) {
          wx.showToast({
            title: "权益不足，无法重试",
            icon: "none",
          });
          return;
        }
      } catch (error) {
        console.warn("权益检查失败，继续执行重试:", error);
      }
    }

    this._isRetrying = true;

    wx.showLoading({
      title: "重试中...",
      mask: true,
    });

    try {
      const devId = currentCamera.id;

      console.log("开始重试切片任务:", {
        originalId: id,
        devId,
        siteId: this.data.siteDetail.id,
        startTime: parseInt(startTime),
        endTime: parseInt(endTime)
      });

      // 提交新的切片任务
      const res = await submitSlicingTask({
        id: devId,
        siteId: this.data.siteDetail.id,
        startTime: parseInt(startTime),
        endTime: parseInt(endTime),
      });

      if (res.code === 0) {
        console.log("重试提交成功，开始删除原任务");

        wx.showToast({
          title: "重试成功",
          icon: "success",
          duration: 2000,
        });

        // 删除原失败任务
        try {
          await deleteSlicingTask({ id: id });
          console.log("原任务删除成功");
        } catch (deleteError) {
          console.error("删除原任务失败:", deleteError);
          // 删除失败不影响重试成功的提示
        }

        // 刷新列表
        await this.getVideoList();
      } else {
        console.error("重试提交失败:", res);
        wx.showToast({
          title: res.msg || "重试失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("重试切片任务异常:", error);

      // 根据错误类型给出不同提示
      let errorMsg = "重试失败";
      if (error.message && error.message.includes("request:fail")) {
        errorMsg = "网络异常，请检查网络连接";
      } else if (error.code === 401 || error.code === 403) {
        errorMsg = "权限不足，请重新登录";
      }

      wx.showToast({
        title: errorMsg,
        icon: "none",
      });
    } finally {
      this._isRetrying = false;
      wx.hideLoading();
    }
  },

  /**
   * 刷新视频 - 回到最新时间并播放
   */
  refreshVideo: function () {
    if (this.data.isRecording) {
      return;
    }

    // 停止当前播放
    this.stopAutoPlay();

    // 获取当前真实时间
    const currentRealTime = Date.now();

    // 获取当前激活摄像头的直播链接
    const cameraIndex = this.data.activateCameraIndex;
    const cameraList = this.data.cameraList;

    if (!cameraList || cameraIndex < 0 || cameraIndex >= cameraList.length) {
      wx.showToast({
        title: "摄像头信息异常",
        icon: "none",
        duration: 2000,
      });
      return;
    }

    const liveUrl = cameraList[cameraIndex].liveUrl;

    // 计算slider值
    const sliderValue = this._timeToSliderValue(currentRealTime);

    // 跳转到最新时间，设置为播放状态
    this.setData({
      isPlaying: false, // 先设置为暂停状态，避免startAutoPlay中的逻辑冲突
      baseTime: currentRealTime,
      currentTime: currentRealTime,
      pausedTime: currentRealTime, // 设置为当前时间，让startAutoPlay从这个时间开始
      sliderValue: sliderValue,
      formattedCurrentTime: this.formatTime(currentRealTime),
      // 更新slider时间范围到最新
      timeRangeEnd: currentRealTime,
      // 设置视频URL为直播链接
      videoUrl: liveUrl,
    });

    // 重新生成刻度
    this.generateTimeScales();

    // 更新录制区域（如果存在）
    if (this.data.isRecording) {
      this._updateRecordAreaNew();
    }

    // 播放视频
    this.playVideo();

    // 确保状态更新完成后再启动自动播放
    wx.nextTick(() => {
      setTimeout(() => {
        // 检查是否还在当前页面且没有被其他操作中断
        if (this.data.currentTime === currentRealTime) {
          this.setData({ isPlaying: true });
          this.startAutoPlay();
        }
      }, 50);
    });

    // 显示提示
    wx.showToast({
      title: "已回到最新时间",
      icon: "success",
      duration: 1500,
    });
  },

  // ==================== 权益和切片任务处理 ====================

  /**
   * 显示权益不足弹窗
   * @param {Object} rights 权益信息
   */
  showNoRightsModal(rights) {
    const content = rights
      ? "您的切片权益已用完，请联系客服"
      : "无法获取权益信息，请检查网络";
    wx.showModal({
      title: "权益不足",
      content,
      showCancel: false,
      confirmText: "确定",
      confirmColor: "#ff6b6b",
    });
  },
  skipTutorial: function () {
    // 跳过教程也要跳转到最新时间并自动播放
    this._completeTutorial();
  },
  startAutoPlay: function () {
    if (this.timer) {
      clearInterval(this.timer);
    }

    // 停止惯性动画
    this._stopInertiaAnimation();

    // 记录播放开始的时间戳
    this.playStartTimestamp = Date.now();

    // 确定播放的起始时间和基准时间
    let playStartTime, baseTime;

    if (this.data.pausedTime > 0) {
      // 从暂停点恢复播放
      playStartTime = this.data.pausedTime;
      baseTime = this.data.baseTime; // 保持原有的baseTime，避免跳动
    } else if (this.data.currentTime && this.data.currentTime !== Date.now()) {
      // 如果当前时间不是实时时间（比如拖拽后的时间），从当前时间开始播放
      playStartTime = this.data.currentTime;
      baseTime = this.data.baseTime || this.data.currentTime;
    } else {
      // 首次播放或重置后播放
      playStartTime = this._alignToSecond(Date.now());
      baseTime = playStartTime;
    }

    this.playStartBaseTime = playStartTime;

    // 判断播放开始时是否在录制区域内，决定是否需要自动暂停
    this.data._shouldAutoPauseAtRecordEnd =
      this._isTimeInRecordArea(playStartTime);

    // 只在首次播放时设置baseTime，暂停恢复时保持不变
    if (this.data.pausedTime === 0) {
      this.setData({
        baseTime: baseTime,
        currentTime: playStartTime,
        formattedCurrentTime: this.formatTime(playStartTime),
        isPlaying: true, // 确保播放状态正确
      });
    } else {
      // 暂停恢复时，清除pausedTime标记，但保持其他状态不变
      this.setData({
        pausedTime: 0,
        isPlaying: true, // 确保播放状态正确
      });
    }

    // 实现超丝滑播放效果 - 优化版本，使用requestAnimationFrame和智能插值
    let frameCount = 0;
    let lastUpdateTime = 0;
    let lastCurrentTime = this.playStartBaseTime;

    const animate = () => {
      // 确保不在拖拽状态且处于播放状态
      if (this.data.isPlaying && !this.data.isDragging) {
        const currentTimestamp = Date.now();
        const elapsedTime = currentTimestamp - this.playStartTimestamp;

        // 计算当前应该显示的时间
        const targetTime = this.playStartBaseTime + elapsedTime;

        // 检查时间边界，如果超出范围则停止播放
        const realCurrentTime = Date.now();
        if (targetTime > realCurrentTime) {
          // 超出当前时间，停止播放并同步到当前时间
          const sliderValue = this._timeToSliderValue(realCurrentTime);
          this.setData({
            currentTime: realCurrentTime,
            baseTime: realCurrentTime,
            sliderValue: sliderValue,
            isPlaying: false,
            formattedCurrentTime: this.formatTime(realCurrentTime),
          });
          this.stopAutoPlay();
          return;
        }

        frameCount++;

        // 使用线性插值实现更平滑的时间过渡
        const deltaTime = currentTimestamp - lastUpdateTime;
        const interpolationFactor = Math.min(deltaTime / 16.67, 1); // 基于60fps的插值因子
        const currentTime =
          lastCurrentTime +
          (targetTime - lastCurrentTime) * interpolationFactor;

        // 更新状态 - 每帧都更新关键状态
        const sliderValue = this._timeToSliderValue(currentTime);
        this.setData({
          currentTime: currentTime,
          sliderValue: sliderValue,
          formattedCurrentTime: this.formatTime(currentTime),
        });

        // 智能DOM更新频率控制
        const shouldUpdateDOM = frameCount % 2 === 0 || deltaTime > 33; // 最低30fps的DOM更新
        if (shouldUpdateDOM) {
          this.generateTimeScales();

          // 如果在录制中，更新录制区域
          if (this.data.isRecording) {
            this._updateRecordAreaNew();
          }
        }

        // 记录当前帧信息
        lastUpdateTime = currentTimestamp;
        lastCurrentTime = currentTime;

        // 使用requestAnimationFrame实现更流畅的动画，回退到setTimeout
        if (typeof requestAnimationFrame !== "undefined") {
          this.timer = requestAnimationFrame(animate);
        } else {
          this.timer = setTimeout(animate, 8); // 约120fps
        }
      }
    };

    // 启动动画循环
    if (typeof requestAnimationFrame !== "undefined") {
      this.timer = requestAnimationFrame(animate);
    } else {
      this.timer = setTimeout(animate, 8);
    }
    this.playVideo();
  },
  // ==================== 切片列表触摸事件处理 ====================

  startClip: async function () {
    // 检查用户权益
    wx.showLoading({
      title: "检查权益中...",
      mask: true,
    });

    try {
      const rights = await this.checkUserRights();

      if (!rights || rights.remain <= 0) {
        wx.hideLoading();
        wx.showModal({
          title: "权益不足",
          content: "您的切片权益已用完，无法进入剪辑状态",
          showCancel: false,
          confirmText: "确定",
          confirmColor: "#ff6b6b",
        });
        return;
      }
    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: "权益检查失败，请重试",
        icon: "none",
        duration: 2000,
      });
      return;
    } finally {
      wx.hideLoading();
    }

    // 获取当前真实时间作为基准时间
    const currentRealTime = Date.now();

    // 计算左侧刻度边界：当前时间减去23小时
    const leftBoundaryTime = currentRealTime - 23 * 60 * 60 * 1000;

    // 更新slider时间范围和当前值
    const sliderValue = this._timeToSliderValue(currentRealTime);

    // 进入剪辑状态时，初始化时间轨道和刻度
    this.setData({
      swipeDeleteItemId: null,
      isClipping: true,
      isPlaying: false, // 先设置为暂停状态，避免startAutoPlay中的逻辑冲突
      baseTime: currentRealTime,
      currentTime: currentRealTime,
      pausedTime: currentRealTime, // 设置为当前时间，让startAutoPlay从这个时间开始
      formattedCurrentTime: this.formatTime(currentRealTime),
      // 新增：设置剪辑开始时间和左边界
      clippingStartTime: currentRealTime,
      leftBoundaryTime: leftBoundaryTime,
      // 更新slider相关数据
      timeRangeStart: leftBoundaryTime,
      timeRangeEnd: currentRealTime,
      sliderValue: sliderValue,
    });

    // 生成时间刻度（首次进入剪辑状态时才生成）
    this.generateTimeScales();

    // 确保状态更新完成后再启动自动播放
    wx.nextTick(() => {
      setTimeout(() => {
        // 检查是否还在剪辑状态且没有被其他操作中断
        if (this.data.isClipping && this.data.currentTime === currentRealTime) {
          this.setData({ isPlaying: true });
          this.startAutoPlay();
        }
      }, 100);
    });

    // 检查是否需要显示新手教程
    this._checkAndShowTutorial();
  },
  /**
   * 启动设备状态轮询
   * @param {string} siteCode 站点编码
   */
  startDevicePolling(siteCode) {
    this._clearTimer("status");
    const timer = setInterval(async () => {
      try {
        await Promise.all([
          this.pollCameraList(siteCode),
          this.pollVideoList(),
        ]);
      } catch (error) {
        console.error("轮询过程中出错:", error);
      }
    }, 5000); // 增加轮询间隔到3秒

    this._setTimer("status", timer);
  },
  stopAutoPlay: function () {
    // 清理定时器（支持setTimeout、setInterval和requestAnimationFrame）
    if (this.timer) {
      clearTimeout(this.timer);
      clearInterval(this.timer);
      // 支持requestAnimationFrame的清理
      if (typeof cancelAnimationFrame !== "undefined") {
        cancelAnimationFrame(this.timer);
      }
      this.timer = null;
    }

    const videoContext = wx.createVideoContext("myVideo", this);
    if (videoContext) {
      videoContext.pause();
    }
  },
  async switchCamera(e) {
    const itemId = e.currentTarget.dataset.id;
    const itemIndex = e.currentTarget.dataset.index;
    if (itemIndex === this.data.activateCameraIndex) return;

    // 防止快速连续切换
    if (this._isSwitchingCamera) {
      return;
    }

    this._isSwitchingCamera = true;

    try {
      // 立即更新摄像头和视频URL
      this.setData({
        activateCameraIndex: itemIndex,
        videoUrl: this.data.cameraList[itemIndex].liveUrl,
        swipeDeleteItemId: null,
      });

      // 立即播放新的视频
      this.playVideo();

      // 移除刷新切片列表的逻辑，只切换摄像头不刷新录像列表
      // await this.getVideoList(); // 已移除
    } catch (error) {
      console.error("切换摄像头失败:", error);
      wx.showToast({
        title: "切换失败",
        icon: "none",
        duration: 1500,
      });
    } finally {
      this._isSwitchingCamera = false;
    }
  },

  togglePlay: function () {
    const isPlaying = !this.data.isPlaying;

    if (!isPlaying) {
      // 暂停轨道播放
      this.setData({
        isPlaying: false,
        pausedTime: this.data.currentTime,
      });

      // 停止自动播放
      this.stopAutoPlay();
    } else {
      // 恢复轨道播放 - 不调用playVideo，只控制轨道动画
      this.setData({
        isPlaying: true,
      });

      // 开始轨道自动播放
      this.startAutoPlay();
    }
  },
  toggleRecord: function () {
    // 如果已经在录制中，询问是否重置录制区域
    if (this.data.isRecording) {
      return;
    }
    // 首次创建录制区域
    this._createNewRecordArea();
  },

  // 取消录制区域
  cancelRecordArea: function () {
    // 只有在录制状态下才能取消
    if (!this.data.isRecording) {
      return;
    }

    // 清除录制状态和录制区域
    this.setData({
      isRecording: false,
      recordStartTime: 0,
      recordEndTime: 0,
      recordStartTimeFormatted: "",
      recordEndTimeFormatted: "",
      recordDurationText: "",
      recordAreaStyle: "display: none;",
      recordAreaAnimation: "",
      // 清除录制拖拽相关状态
      isRecordAreaDragging: false,
      recordDragType: "",
      recordDragStartX: 0,
      recordDragOriginalStartTime: 0,
      recordDragOriginalEndTime: 0,
    });

    // 重新生成刻度，确保界面正常显示
    this.generateTimeScales();

    wx.showToast({
      title: "已取消录制区域",
      icon: "success",
      duration: 1500,
    });
  },
  updateFormattedTime: function () {
    this.setData({
      formattedCurrentTime: this.formatTime(this.data.currentTime),
    });
  },

  // ==================== 视频事件处理 ====================

  /**
   * 视频数据加载完成事件
   */
  onVideoLoadedData: function (e) {
    this._videoLoadedData = true;
  },

  /**
   * 视频可以播放事件
   */
  onVideoCanPlay: function (e) {
    this._videoCanPlay = true;

    // 如果是拖拽后的回放视频，且需要暂停显示画面
    if (this._needPauseAfterLoad) {
      this._needPauseAfterLoad = false;

      setTimeout(() => {
        const videoContext = wx.createVideoContext("myVideo", this);
        if (videoContext) {
          // 先播放一小段时间让视频显示画面
          videoContext.play();

          setTimeout(() => {
            videoContext.pause();
          }, 300);
        }
      }, 100);
    }
  },

  /**
   * 视频播放事件 - 不影响轨道播放状态
   */
  onVideoPlay: function (e) {
    // 视频播放状态不影响轨道播放动画
    // 轨道播放状态由 isPlaying 独立控制
  },

  /**
   * 视频暂停事件 - 不影响轨道播放状态
   */
  onVideoPause: function (e) {
    // 视频暂停状态不影响轨道播放动画
  },

  /**
   * 视频错误事件 - 不影响轨道播放状态
   */
  onVideoError: function (e) {
    console.error("视频播放错误:", e);
    // 视频播放错误不影响轨道播放动画
    // 轨道可以继续播放，显示时间进度
    wx.showToast({
      title: "视频加载失败，轨道继续播放",
      icon: "none",
      duration: 2000,
    });
  },

  /**
   * 放大刻度（缩小时间间隔）
   * 检查录制状态和缩放权限，执行刻度放大操作并显示提示
   */
  zoomIn: async function () {
    // 录制状态下不允许缩放
    if (this.data.isRecording) {
      return;
    }

    // 检查是否可以放大
    if (!this.data.canZoomIn) {
      return;
    }

    // 防止快速连续点击
    if (this._isScaleSwitching || this._scaleOperationPromise) {
      return;
    }

    // 刻度配置数组
    const scaleConfigs = [
      { unit: "second", text: "秒", interval: 1000 }, // 1秒间隔
      { unit: "minute", text: "分钟", interval: 60000 }, // 1分钟间隔
      { unit: "10minute", text: "10分钟", interval: 600000 }, // 10分钟间隔
    ];

    const currentLevel = this.data.scaleLevel;
    const newLevel = currentLevel - 1;
    const newConfig = scaleConfigs[newLevel];

    // 计算新的pixelsPerSecond，保持刻度间隔为10px
    const pixelsPerSecond = 10000 / newConfig.interval;

    try {
      // 使用Promise化的缩放操作，确保完成后才允许下一次操作
      this._scaleOperationPromise = this._updateScaleAsync(
        newLevel,
        newConfig,
        pixelsPerSecond,
        "放大",
      );

      await this._scaleOperationPromise;

      // 显示刻度切换提示
      wx.showToast({
        title: `当前刻度已切换至 ${newConfig.text}`,
        icon: "none",
        duration: 1500,
      });
    } catch (error) {
      console.error("缩放操作失败:", error);
      wx.showToast({
        title: "缩放操作失败，请重试",
        icon: "none",
        duration: 1500,
      });
    } finally {
      // 清除操作Promise，允许下一次操作
      this._scaleOperationPromise = null;
    }
  },

  /**
   * 缩小刻度（扩大时间间隔）
   * 检查录制状态和缩放权限，执行刻度缩小操作并显示提示
   */
  zoomOut: async function () {
    // 录制状态下不允许缩放
    if (this.data.isRecording) {
      return;
    }

    // 检查是否可以缩小
    if (!this.data.canZoomOut) {
      return;
    }

    // 防止快速连续点击
    if (this._isScaleSwitching || this._scaleOperationPromise) {
      return;
    }

    // 刻度配置数组
    const scaleConfigs = [
      { unit: "second", text: "秒", interval: 1000 }, // 1秒间隔
      { unit: "minute", text: "分钟", interval: 60000 }, // 1分钟间隔
      { unit: "10minute", text: "10分钟", interval: 600000 }, // 10分钟间隔
    ];

    const currentLevel = this.data.scaleLevel;
    const newLevel = currentLevel + 1;
    const newConfig = scaleConfigs[newLevel];

    // 计算新的pixelsPerSecond，保持刻度间隔为10px
    const pixelsPerSecond = 10000 / newConfig.interval;

    try {
      // 使用Promise化的缩放操作，确保完成后才允许下一次操作
      this._scaleOperationPromise = this._updateScaleAsync(
        newLevel,
        newConfig,
        pixelsPerSecond,
        "缩小",
      );

      await this._scaleOperationPromise;

      // 显示刻度切换提示
      wx.showToast({
        title: `当前刻度已切换至 ${newConfig.text}`,
        icon: "none",
        duration: 1500,
      });
    } catch (error) {
      console.error("缩放操作失败:", error);
      wx.showToast({
        title: "缩放操作失败，请重试",
        icon: "none",
        duration: 1500,
      });
    } finally {
      // 清除操作Promise，允许下一次操作
      this._scaleOperationPromise = null;
    }
  },

  /**
   * 防止页面滚动
   */
  preventPageScroll: function (e) {
    // 阻止默认的滚动行为
    return false;
  },
});
